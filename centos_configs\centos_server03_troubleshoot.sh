#!/bin/bash
# CentOS Server03 故障排除脚本
# 用于诊断和修复WEB服务器2的问题

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[0;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 全局变量
SERVER_IP="***************"
DOMAIN="mufeng.yuanchu"

# 打印带颜色的信息
print_info() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_header() {
    echo -e "${BLUE}[HEADER]${NC} $1"
}

# 检查Apache服务状态
check_apache_status() {
    print_header "检查Apache服务状态..."
    
    if systemctl is-active --quiet httpd; then
        print_success "Apache服务正在运行"
    else
        print_error "Apache服务未运行"
        print_info "尝试启动Apache服务..."
        systemctl start httpd
    fi
    
    # 显示详细状态
    print_info "Apache服务详细状态:"
    systemctl status httpd --no-pager -l
    
    # 检查端口监听
    print_info "检查端口监听状态:"
    ss -tlnp | grep -E ':80|:443'
}

# 检查Apache配置
check_apache_config() {
    print_header "检查Apache配置..."
    
    # 测试配置文件语法
    print_info "测试Apache配置文件语法..."
    if httpd -t; then
        print_success "Apache配置文件语法正确"
    else
        print_error "Apache配置文件语法错误"
        return 1
    fi
    
    # 检查虚拟主机配置
    print_info "检查虚拟主机配置文件..."
    
    if [ -f "/etc/httpd/conf.d/www-ssl.conf" ]; then
        print_success "HTTPS虚拟主机配置文件存在"
        print_info "配置文件内容:"
        cat /etc/httpd/conf.d/www-ssl.conf
    else
        print_error "HTTPS虚拟主机配置文件不存在"
    fi
    
    if [ -f "/etc/httpd/conf.d/www-redirect.conf" ]; then
        print_success "HTTP重定向配置文件存在"
    else
        print_error "HTTP重定向配置文件不存在"
    fi
}

# 检查SSL证书
check_ssl_certificates() {
    print_header "检查SSL证书..."
    
    local cert_file="/etc/pki/tls/certs/www.$DOMAIN.crt"
    local key_file="/etc/pki/tls/private/www.$DOMAIN.key"
    
    if [ -f "$cert_file" ]; then
        print_success "SSL证书文件存在: $cert_file"
        print_info "证书信息:"
        openssl x509 -in "$cert_file" -text -noout | grep -E "Subject:|Not Before:|Not After:"
    else
        print_error "SSL证书文件不存在: $cert_file"
    fi
    
    if [ -f "$key_file" ]; then
        print_success "SSL私钥文件存在: $key_file"
        print_info "私钥权限: $(ls -l $key_file)"
    else
        print_error "SSL私钥文件不存在: $key_file"
    fi
}

# 检查网站文件
check_website_files() {
    print_header "检查网站文件..."
    
    local doc_root="/var/www/yuanchu"
    local virtual_dir="/var/www/yuanchu/xuni"
    
    if [ -d "$doc_root" ]; then
        print_success "网站根目录存在: $doc_root"
        print_info "目录权限: $(ls -ld $doc_root)"
        print_info "目录内容:"
        ls -la "$doc_root"
    else
        print_error "网站根目录不存在: $doc_root"
    fi
    
    if [ -f "$doc_root/yuanchu.html" ]; then
        print_success "主页文件存在: yuanchu.html"
    else
        print_error "主页文件不存在: yuanchu.html"
    fi
    
    if [ -d "$virtual_dir" ]; then
        print_success "虚拟目录存在: $virtual_dir"
        print_info "虚拟目录内容:"
        ls -la "$virtual_dir"
    else
        print_error "虚拟目录不存在: $virtual_dir"
    fi
}

# 检查防火墙
check_firewall() {
    print_header "检查防火墙配置..."
    
    if systemctl is-active --quiet firewalld; then
        print_success "firewalld服务正在运行"
        
        print_info "当前防火墙规则:"
        firewall-cmd --list-all
        
        # 检查具体端口
        if firewall-cmd --query-service=http; then
            print_success "HTTP服务已开放"
        else
            print_warning "HTTP服务未开放"
        fi
        
        if firewall-cmd --query-service=https; then
            print_success "HTTPS服务已开放"
        else
            print_warning "HTTPS服务未开放"
        fi
    else
        print_warning "firewalld服务未运行"
    fi
}

# 检查SELinux
check_selinux() {
    print_header "检查SELinux配置..."
    
    if command -v getenforce >/dev/null 2>&1; then
        local selinux_status=$(getenforce)
        print_info "SELinux状态: $selinux_status"
        
        if [ "$selinux_status" = "Enforcing" ]; then
            print_info "检查Apache相关的SELinux布尔值:"
            getsebool httpd_can_network_connect
            
            print_info "检查文件SELinux上下文:"
            ls -Z /var/www/yuanchu/ 2>/dev/null || print_warning "无法检查SELinux上下文"
        fi
    else
        print_info "SELinux不可用"
    fi
}

# 测试本地访问
test_local_access() {
    print_header "测试本地访问..."
    
    # 测试HTTP重定向
    print_info "测试HTTP重定向..."
    local http_response=$(curl -s -I http://localhost/ 2>/dev/null | head -n1)
    print_info "HTTP响应: $http_response"
    
    # 测试HTTPS访问
    print_info "测试HTTPS主页访问..."
    if curl -k -s https://localhost/yuanchu.html >/dev/null 2>&1; then
        print_success "HTTPS主页可以访问"
    else
        print_error "HTTPS主页无法访问"
        print_info "详细错误信息:"
        curl -k -v https://localhost/yuanchu.html 2>&1 | head -20
    fi
    
    # 测试虚拟目录
    print_info "测试虚拟目录访问..."
    if curl -k -s https://localhost/xuni/ >/dev/null 2>&1; then
        print_success "虚拟目录可以访问"
    else
        print_error "虚拟目录无法访问"
        print_info "详细错误信息:"
        curl -k -v https://localhost/xuni/ 2>&1 | head -20
    fi
}

# 检查Apache日志
check_apache_logs() {
    print_header "检查Apache日志..."
    
    local error_log="/var/log/httpd/error_log"
    local access_log="/var/log/httpd/access_log"
    local ssl_error_log="/var/log/httpd/www-ssl-error.log"
    
    if [ -f "$error_log" ]; then
        print_info "最近的Apache错误日志:"
        tail -20 "$error_log"
    fi
    
    if [ -f "$ssl_error_log" ]; then
        print_info "最近的SSL错误日志:"
        tail -10 "$ssl_error_log"
    fi
    
    if [ -f "$access_log" ]; then
        print_info "最近的访问日志:"
        tail -10 "$access_log"
    fi
}

# 修复常见问题
fix_common_issues() {
    print_header "修复常见问题..."
    
    # 重新创建主页文件（如果不存在）
    if [ ! -f "/var/www/yuanchu/yuanchu.html" ]; then
        print_info "重新创建主页文件..."
        mkdir -p /var/www/yuanchu
        cat > /var/www/yuanchu/yuanchu.html << 'EOF'
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Welcome to Mufeng's CentOS Website</title>
</head>
<body>
    <h1>Welcome to Mufeng's CentOS Website</h1>
    <p>Server03 - CentOS Web服务器2</p>
    <p>HTTPS服务正常运行</p>
</body>
</html>
EOF
        print_success "主页文件已重新创建"
    fi
    
    # 重新创建虚拟目录（如果不存在）
    if [ ! -d "/var/www/yuanchu/xuni" ]; then
        print_info "重新创建虚拟目录..."
        mkdir -p /var/www/yuanchu/xuni
        cat > /var/www/yuanchu/xuni/index.html << 'EOF'
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Mufeng Information - CentOS Virtual Directory</title>
</head>
<body>
    <h1>Mufeng Information</h1>
    <p>CentOS 虚拟目录 - /xuni</p>
    <p>虚拟目录访问正常</p>
</body>
</html>
EOF
        print_success "虚拟目录已重新创建"
    fi
    
    # 修复文件权限
    print_info "修复文件权限..."
    chown -R apache:apache /var/www/yuanchu
    chmod -R 755 /var/www/yuanchu
    
    # 修复SELinux上下文
    if command -v restorecon >/dev/null 2>&1; then
        print_info "修复SELinux上下文..."
        restorecon -R /var/www/yuanchu
    fi
    
    # 重启Apache服务
    print_info "重启Apache服务..."
    systemctl restart httpd
    
    if systemctl is-active --quiet httpd; then
        print_success "Apache服务重启成功"
    else
        print_error "Apache服务重启失败"
    fi
}

# 生成故障排除报告
generate_troubleshoot_report() {
    print_header "生成故障排除报告..."
    
    local report_file="centos_server03_troubleshoot_$(date +%Y%m%d_%H%M%S).txt"
    
    cat > $report_file << EOF
CentOS Server03 故障排除报告
生成时间: $(date)
服务器IP: $SERVER_IP

检查项目:
1. Apache服务状态
2. Apache配置文件
3. SSL证书配置
4. 网站文件检查
5. 防火墙配置
6. SELinux配置
7. 本地访问测试
8. Apache日志检查

详细结果请查看控制台输出。

建议的解决步骤:
1. 检查Apache配置文件语法
2. 验证SSL证书文件存在
3. 确认网站文件权限正确
4. 检查防火墙规则
5. 验证SELinux上下文
6. 查看Apache错误日志

访问地址:
- HTTPS主页: https://$SERVER_IP/yuanchu.html
- 虚拟目录: https://$SERVER_IP/xuni/
EOF
    
    print_success "故障排除报告已生成: $report_file"
}

# 主函数
main() {
    print_header "CentOS Server03 故障排除工具"
    print_info "开始诊断WEB服务器2的问题..."
    echo ""
    
    check_apache_status
    echo ""
    check_apache_config
    echo ""
    check_ssl_certificates
    echo ""
    check_website_files
    echo ""
    check_firewall
    echo ""
    check_selinux
    echo ""
    test_local_access
    echo ""
    check_apache_logs
    echo ""
    
    read -p "是否尝试修复常见问题? (y/n): " fix_choice
    if [[ "$fix_choice" =~ ^[Yy]$ ]]; then
        echo ""
        fix_common_issues
        echo ""
        print_info "修复完成，重新测试..."
        test_local_access
    fi
    
    echo ""
    generate_troubleshoot_report
    
    print_success "故障排除完成!"
    print_info "如果问题仍然存在，请查看生成的报告文件"
}

# 执行主函数
main "$@"
