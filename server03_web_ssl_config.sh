#!/bin/bash
# Server03 - WEB服务器2配置脚本
# 系统: Ubuntu
# 功能: Apache + SSL + 虚拟目录，端口80,443

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[0;33m'
NC='\033[0m' # No Color

# 全局变量
SERVER_IP=""
SUBNET_MASK=""

# 打印带颜色的信息
print_info() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

# 检查是否以root用户运行
if [ "$EUID" -ne 0 ]; then
    print_error "请以root用户运行此脚本"
    exit 1
fi

# 设置固定的服务IP配置
get_ip_config() {
    print_info "WEB服务器2固定IP配置："

    # 固定IP分配
    SERVER_IP="***************"
    SUBNET_MASK="24"

    print_info "WEB服务器2 IP: $SERVER_IP/$SUBNET_MASK"
    print_info "域名: www.mufeng.yuanchu"

    read -p "确认使用以上固定配置? (y/n): " confirm
    if [[ ! "$confirm" =~ ^[Yy]$ ]]; then
        print_error "用户取消配置"
        exit 1
    fi
}

# 添加服务IP地址
setup_ip() {
    print_info "正在为WEB服务添加IP地址 $SERVER_IP/$SUBNET_MASK..."

    # 获取网络接口名称
    INTERFACE=$(ip -o -4 route show to default | awk '{print $5}')

    if [ -z "$INTERFACE" ]; then
        print_error "无法确定网络接口"
        exit 1
    fi

    print_info "在网络接口 $INTERFACE 上添加服务IP地址..."

    # 检查IP是否已经存在
    if ip addr show $INTERFACE | grep -q "$SERVER_IP"; then
        print_warning "IP地址 $SERVER_IP 已经存在于接口 $INTERFACE 上"
        return 0
    fi

    # 添加IP地址到现有接口（不删除原有配置）
    if ip addr add $SERVER_IP/$SUBNET_MASK dev $INTERFACE 2>/dev/null; then
        print_info "成功添加IP地址 $SERVER_IP 到接口 $INTERFACE"
    else
        # 如果添加失败，可能是因为IP已存在，检查是否真的存在
        if ip addr show $INTERFACE | grep -q "$SERVER_IP"; then
            print_warning "IP地址 $SERVER_IP 已经存在，跳过添加"
        else
            print_error "添加IP地址失败"
            exit 1
        fi
    fi

    # 创建持久化配置（Ubuntu方式）
    print_info "创建持久化IP配置..."

    # 创建网络脚本来在启动时添加IP
    cat > /etc/systemd/system/web2-service-ip.service << EOF
[Unit]
Description=Add Web2 Service IP Address
After=network.target

[Service]
Type=oneshot
ExecStart=/bin/ip addr add $SERVER_IP/$SUBNET_MASK dev $INTERFACE
RemainAfterExit=yes
StandardOutput=journal

[Install]
WantedBy=multi-user.target
EOF

    # 启用服务
    systemctl enable web2-service-ip.service

    # 验证IP配置
    sleep 2
    print_info "当前接口 $INTERFACE 的IP配置："
    ip addr show $INTERFACE | grep inet

    print_info "服务IP地址添加完成，已配置开机自启"
}

# 安装Apache和SSL
install_httpd_ssl() {
    print_info "正在安装Apache和SSL..."

    # 检查网络连接
    if ! ping -c 1 ******* >/dev/null 2>&1; then
        print_warning "网络连接异常，请检查网络设置"
    fi

    # 更新系统
    apt update -y

    # 安装Apache和SSL
    if ! apt install -y apache2 openssl; then
        print_error "Apache安装失败，请检查网络连接或软件源配置"
        exit 1
    fi

    # 检查Apache是否安装成功
    if ! command -v apache2 >/dev/null 2>&1; then
        print_error "Apache安装失败"
        exit 1
    fi

    # 启用SSL模块
    if command -v a2enmod >/dev/null 2>&1; then
        a2enmod ssl 2>/dev/null || true
        a2enmod rewrite 2>/dev/null || true
    else
        print_warning "a2enmod命令不可用，请手动启用SSL模块"
    fi

    print_info "Apache和SSL安装完成"
}

# 创建网站目录和内容
create_website() {
    print_info "正在创建网站目录和内容..."

    # 创建网站目录
    mkdir -p /www/yuanchu/xuni

    # 创建主页
    cat > /www/yuanchu/yuanchu.html << 'EOF'
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Welcome to Mufeng's Website</title>
    <style>
        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            margin: 0;
            padding: 40px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
            overflow: hidden;
        }
        .header {
            background: linear-gradient(135deg, #2c3e50 0%, #34495e 100%);
            color: white;
            padding: 30px;
            text-align: center;
        }
        .header h1 {
            margin: 0;
            font-size: 2.5em;
            font-weight: 300;
        }
        .header p {
            margin: 10px 0 0 0;
            opacity: 0.9;
            font-size: 1.1em;
        }
        .info {
            padding: 30px;
        }
        .info h2 {
            color: #2c3e50;
            border-bottom: 2px solid #3498db;
            padding-bottom: 10px;
            margin-bottom: 20px;
        }
        .info-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin: 20px 0;
        }
        .info-card {
            background: #f8f9fa;
            padding: 20px;
            border-radius: 10px;
            border-left: 4px solid #3498db;
        }
        .info-card h3 {
            margin: 0 0 15px 0;
            color: #2c3e50;
        }
        .info-list {
            list-style: none;
            padding: 0;
            margin: 0;
        }
        .info-list li {
            padding: 8px 0;
            border-bottom: 1px solid #ecf0f1;
        }
        .info-list li:last-child {
            border-bottom: none;
        }
        .links {
            background: #ecf0f1;
            padding: 20px;
            border-radius: 10px;
            margin-top: 20px;
        }
        .links a {
            display: inline-block;
            background: #3498db;
            color: white;
            padding: 12px 24px;
            text-decoration: none;
            border-radius: 25px;
            margin: 5px 10px 5px 0;
            transition: all 0.3s ease;
        }
        .links a:hover {
            background: #2980b9;
            transform: translateY(-2px);
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>Welcome to Mufeng's Website</h1>
            <p>Server03 - Web服务器2</p>
        </div>

        <div class="info">
            <div class="info-grid">
                <div class="info-card">
                    <h3>服务器信息</h3>
                    <ul class="info-list">
                        <li><strong>IP地址:</strong> ***************</li>
                        <li><strong>域名:</strong> www.mufeng.yuanchu</li>
                        <li><strong>服务:</strong> Apache + SSL</li>
                        <li><strong>端口:</strong> 80, 443</li>
                        <li><strong>SSL证书:</strong> 自签名证书</li>
                        <li><strong>状态:</strong> 正常运行</li>
                    </ul>
                </div>

                <div class="info-card">
                    <h3>技术特性</h3>
                    <ul class="info-list">
                        <li><strong>HTTP重定向:</strong> 自动跳转HTTPS</li>
                        <li><strong>虚拟目录:</strong> 支持</li>
                        <li><strong>SSL加密:</strong> 启用</li>
                        <li><strong>字符编码:</strong> UTF-8</li>
                        <li><strong>响应式设计:</strong> 支持</li>
                    </ul>
                </div>
            </div>

            <div class="links">
                <h2>访问链接</h2>
                <a href="/">主页 (HTTPS)</a>
                <a href="/xuni/">虚拟目录 (HTTPS)</a>
            </div>
        </div>
    </div>
</body>
</html>
EOF

    # 创建虚拟目录页面
    cat > /www/yuanchu/xuni/index.html << 'EOF'
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Mufeng Information - Virtual Directory</title>
    <style>
        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            margin: 0;
            padding: 40px;
            background: linear-gradient(135deg, #74b9ff 0%, #0984e3 100%);
            min-height: 100vh;
        }
        .container {
            max-width: 700px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
            overflow: hidden;
        }
        .header {
            background: linear-gradient(135deg, #00b894 0%, #00a085 100%);
            color: white;
            padding: 30px;
            text-align: center;
        }
        .header h1 {
            margin: 0;
            font-size: 2.2em;
            font-weight: 300;
        }
        .header p {
            margin: 10px 0 0 0;
            opacity: 0.9;
            font-size: 1.1em;
        }
        .content {
            padding: 30px;
        }
        .content h2 {
            color: #2d3436;
            border-bottom: 2px solid #00b894;
            padding-bottom: 10px;
            margin-bottom: 20px;
        }
        .info-box {
            background: #f1f2f6;
            padding: 20px;
            border-radius: 10px;
            margin: 20px 0;
            border-left: 4px solid #00b894;
        }
        .path-info {
            background: #e8f4fd;
            padding: 15px;
            border-radius: 8px;
            margin: 15px 0;
            font-family: 'Courier New', monospace;
        }
        .path-info strong {
            color: #0984e3;
        }
        .nav-button {
            display: inline-block;
            background: #00b894;
            color: white;
            padding: 12px 24px;
            text-decoration: none;
            border-radius: 25px;
            margin: 20px 0;
            transition: all 0.3s ease;
        }
        .nav-button:hover {
            background: #00a085;
            transform: translateY(-2px);
        }
        .feature-list {
            list-style: none;
            padding: 0;
        }
        .feature-list li {
            padding: 10px 0;
            border-bottom: 1px solid #ddd;
        }
        .feature-list li:last-child {
            border-bottom: none;
        }
        .feature-list li:before {
            content: "✓";
            color: #00b894;
            font-weight: bold;
            margin-right: 10px;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>Mufeng Information</h1>
            <p>虚拟目录 - /xuni</p>
        </div>

        <div class="content">
            <h2>虚拟目录信息</h2>

            <div class="info-box">
                <p>这是一个Apache虚拟目录示例，展示了如何配置虚拟目录访问。虚拟目录允许将不同的物理路径映射到Web服务器的URL路径中。</p>
            </div>

            <h2>路径配置</h2>
            <div class="path-info">
                <strong>物理路径:</strong> /www/yuanchu/xuni<br>
                <strong>虚拟路径:</strong> /xuni<br>
                <strong>访问方式:</strong> https://www.mufeng.yuanchu/xuni/
            </div>

            <h2>技术特性</h2>
            <ul class="feature-list">
                <li>Apache Alias指令配置</li>
                <li>独立的目录权限控制</li>
                <li>支持HTTPS安全访问</li>
                <li>UTF-8字符编码支持</li>
                <li>响应式页面设计</li>
            </ul>

            <a href="../yuanchu.html" class="nav-button">返回主页</a>
        </div>
    </div>
</body>
</html>
EOF

    # 设置权限
    chown -R www-data:www-data /www
    chmod -R 755 /www

    print_info "网站目录和内容创建完成"
}

# 生成SSL证书
generate_ssl_cert() {
    print_info "正在生成SSL证书..."

    # 创建SSL目录
    mkdir -p /etc/apache2/ssl

    # 生成自签名证书
    openssl req -x509 -nodes -days 365 -newkey rsa:2048 \
        -keyout /etc/apache2/ssl/server.key \
        -out /etc/apache2/ssl/server.crt \
        -subj "/C=CN/ST=Beijing/L=Beijing/O=Mufeng/OU=IT/CN=www.mufeng.yuanchu"

    # 设置权限
    chmod 600 /etc/apache2/ssl/server.key

    print_info "SSL证书生成完成"
}

# 配置Apache虚拟主机
configure_httpd() {
    print_info "正在配置Apache虚拟主机..."

    # 检查Apache配置目录是否存在
    if [ ! -d "/etc/apache2/sites-available" ]; then
        print_error "Apache配置目录不存在，请确认Apache已正确安装"
        exit 1
    fi

    # 创建虚拟主机配置
    cat > /etc/apache2/sites-available/yuanchu.conf << EOF
<VirtualHost *:80>
    ServerName www.mufeng.yuanchu
    ServerAlias $SERVER_IP
    ServerAlias localhost

    # 强制重定向到HTTPS
    RewriteEngine On
    RewriteCond %{HTTPS} off
    RewriteRule ^(.*)$ https://%{HTTP_HOST}%{REQUEST_URI} [L,R=301]

    # 备用重定向方法
    Redirect permanent / https://www.mufeng.yuanchu/
</VirtualHost>

<VirtualHost *:443>
    ServerName www.mufeng.yuanchu
    ServerAlias $SERVER_IP
    ServerAlias localhost
    DocumentRoot /www/yuanchu
    DirectoryIndex yuanchu.html index.html

    SSLEngine on
    SSLCertificateFile /etc/apache2/ssl/server.crt
    SSLCertificateKeyFile /etc/apache2/ssl/server.key

    <Directory /www/yuanchu>
        AllowOverride None
        Require all granted
        Options Indexes FollowSymLinks
    </Directory>

    Alias /xuni /www/yuanchu/xuni

    <Directory /www/yuanchu/xuni>
        AllowOverride None
        Require all granted
        Options Indexes FollowSymLinks
        DirectoryIndex index.html
    </Directory>

    ErrorLog \${APACHE_LOG_DIR}/yuanchu-error.log
    CustomLog \${APACHE_LOG_DIR}/yuanchu-access.log combined
</VirtualHost>
EOF

    # 启用站点
    if command -v a2ensite >/dev/null 2>&1; then
        a2ensite yuanchu.conf
        a2dissite 000-default.conf 2>/dev/null || true
    else
        print_warning "a2ensite命令不可用，请手动启用站点配置"
        # 手动创建符号链接
        ln -sf /etc/apache2/sites-available/yuanchu.conf /etc/apache2/sites-enabled/
        rm -f /etc/apache2/sites-enabled/000-default.conf
    fi

    print_info "Apache虚拟主机配置完成"
}

# 配置防火墙
configure_firewall() {
    print_info "正在配置防火墙..."

    # 检查ufw状态
    if ! ufw status | grep -q "Status: active"; then
        ufw --force enable
    fi

    # 添加HTTP和HTTPS服务到防火墙
    ufw allow 80/tcp
    ufw allow 443/tcp

    print_info "防火墙配置完成"
}

# 启动Apache
start_httpd() {
    print_info "正在启动Apache..."

    # 检查配置
    if command -v apache2ctl >/dev/null 2>&1; then
        apache2ctl configtest
    elif command -v apache2 >/dev/null 2>&1; then
        apache2 -t
    else
        print_warning "无法找到Apache配置测试命令"
    fi

    # 启动服务
    if systemctl list-unit-files | grep -q apache2.service; then
        systemctl enable apache2
        systemctl restart apache2

        # 检查服务状态
        if systemctl is-active --quiet apache2; then
            print_info "Apache服务启动成功"
        else
            print_error "Apache服务启动失败"
            # 显示错误日志
            journalctl -u apache2 --no-pager -l
            exit 1
        fi
    else
        print_error "Apache服务单元文件不存在，请确认Apache已正确安装"
        exit 1
    fi
}

# 测试Web服务
test_web() {
    print_info "正在测试Web服务..."

    # 测试HTTP重定向
    curl -s -I http://localhost | grep -q "301 Moved Permanently"
    if [ $? -eq 0 ]; then
        print_info "HTTP重定向测试成功"
    else
        print_warning "HTTP重定向测试失败"
    fi

    # 测试HTTPS
    curl -s -k https://localhost/yuanchu.html | grep -q "Welcome to Mufeng's Website"
    if [ $? -eq 0 ]; then
        print_info "HTTPS主页测试成功"
    else
        print_warning "HTTPS主页测试失败"
    fi

    # 测试虚拟目录
    curl -s -k https://localhost/xuni/ | grep -q "Mufeng Information"
    if [ $? -eq 0 ]; then
        print_info "虚拟目录测试成功"
    else
        print_warning "虚拟目录测试失败"
    fi

    print_info "Web服务测试完成"
}

# 添加hosts条目
add_hosts_entry() {
    print_info "正在添加hosts条目..."

    # 检查是否已存在
    if grep -q "www.mufeng.yuanchu" /etc/hosts; then
        print_info "hosts条目已存在"
    else
        # 添加hosts条目
        cat >> /etc/hosts << EOF

# Mufeng.yuanchu SSL网站
$SERVER_IP www.mufeng.yuanchu
127.0.0.1 www.mufeng.yuanchu
EOF
        print_info "hosts条目添加完成"
    fi
}

# 主函数
main() {
    print_info "开始配置Server03 - WEB服务器2 (Ubuntu)..."

    # 获取IP配置
    get_ip_config

    # 询问是否添加服务IP地址
    read -p "是否为WEB服务添加IP地址$SERVER_IP/$SUBNET_MASK? (不会影响现有网络配置) (y/n): " answer
    if [[ "$answer" =~ ^[Yy]$ ]]; then
        setup_ip
    fi

    # 安装和配置服务
    install_httpd_ssl
    create_website
    generate_ssl_cert
    configure_httpd
    configure_firewall
    start_httpd
    add_hosts_entry
    test_web

    print_info "Server03 - WEB服务器2配置完成!"
    print_info "服务器地址: $SERVER_IP"
    print_info "访问地址: https://www.mufeng.yuanchu"
    print_info "虚拟目录: https://www.mufeng.yuanchu/xuni/"
    print_info "服务: Apache + SSL"
    print_info "端口: 80 (重定向到443), 443"
    print_info "请确保DNS服务器已正确配置www.mufeng.yuanchu指向$SERVER_IP"
}

# 执行主函数
main
