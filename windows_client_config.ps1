# Windows客户端DNS配置脚本
# PowerShell脚本，用于在Windows系统上配置DNS设置

# 服务器配置
$DNS_SERVER = "***************"
$WEB1_SERVER = "***************"
$WEB2_SERVER = "***************"
$DOMAIN = "mufeng.yuanchu"

# 颜色输出函数
function Write-ColorOutput {
    param(
        [string]$Message,
        [string]$Color = "White"
    )
    Write-Host $Message -ForegroundColor $Color
}

function Write-Info {
    param([string]$Message)
    Write-ColorOutput "[INFO] $Message" "Green"
}

function Write-Warning {
    param([string]$Message)
    Write-ColorOutput "[WARNING] $Message" "Yellow"
}

function Write-Error {
    param([string]$Message)
    Write-ColorOutput "[ERROR] $Message" "Red"
}

function Write-Success {
    param([string]$Message)
    Write-ColorOutput "[SUCCESS] $Message" "Cyan"
}

function Write-Header {
    param([string]$Message)
    Write-ColorOutput "[HEADER] $Message" "Magenta"
}

# 检查管理员权限
function Test-Administrator {
    $currentUser = [Security.Principal.WindowsIdentity]::GetCurrent()
    $principal = New-Object Security.Principal.WindowsPrincipal($currentUser)
    return $principal.IsInRole([Security.Principal.WindowsBuiltInRole]::Administrator)
}

# 获取网络适配器
function Get-ActiveNetworkAdapter {
    try {
        $adapter = Get-NetAdapter | Where-Object { $_.Status -eq "Up" -and $_.InterfaceType -eq "Ethernet" -or $_.InterfaceType -eq "Wireless80211" } | Select-Object -First 1
        return $adapter
    }
    catch {
        Write-Error "无法获取网络适配器: $($_.Exception.Message)"
        return $null
    }
}

# 配置DNS设置
function Set-DNSConfiguration {
    Write-Header "配置Windows DNS设置..."
    
    if (-not (Test-Administrator)) {
        Write-Error "需要管理员权限来配置DNS设置"
        Write-Info "请以管理员身份运行PowerShell"
        return $false
    }
    
    try {
        # 获取活动网络适配器
        $adapter = Get-ActiveNetworkAdapter
        if ($null -eq $adapter) {
            Write-Error "未找到活动的网络适配器"
            return $false
        }
        
        Write-Info "使用网络适配器: $($adapter.Name)"
        
        # 设置DNS服务器
        Set-DnsClientServerAddress -InterfaceIndex $adapter.InterfaceIndex -ServerAddresses $DNS_SERVER, "*******"
        
        Write-Success "DNS配置完成"
        Write-Info "首选DNS: $DNS_SERVER"
        Write-Info "备用DNS: *******"
        
        return $true
    }
    catch {
        Write-Error "DNS配置失败: $($_.Exception.Message)"
        return $false
    }
}

# 配置hosts文件
function Set-HostsFile {
    Write-Header "配置Windows hosts文件..."
    
    if (-not (Test-Administrator)) {
        Write-Error "需要管理员权限来修改hosts文件"
        return $false
    }
    
    try {
        $hostsPath = "$env:SystemRoot\System32\drivers\etc\hosts"
        
        # 备份hosts文件
        $backupPath = "$hostsPath.backup_$(Get-Date -Format 'yyyyMMdd_HHmmss')"
        Copy-Item $hostsPath $backupPath
        Write-Info "已备份hosts文件到: $backupPath"
        
        # 检查是否已存在配置
        $hostsContent = Get-Content $hostsPath
        $domainExists = $hostsContent | Where-Object { $_ -match $DOMAIN }
        
        if ($domainExists) {
            Write-Warning "hosts文件中已存在 $DOMAIN 相关配置"
            $overwrite = Read-Host "是否覆盖现有配置? (y/n)"
            if ($overwrite -ne "y" -and $overwrite -ne "Y") {
                Write-Info "跳过hosts文件配置"
                return $true
            }
            
            # 移除现有配置
            $newContent = $hostsContent | Where-Object { $_ -notmatch $DOMAIN }
            Set-Content $hostsPath $newContent
        }
        
        # 添加新配置
        $hostsEntries = @(
            "",
            "# Mufeng.yuanchu 服务器配置",
            "$DNS_SERVER dns.$DOMAIN",
            "$WEB1_SERVER web.$DOMAIN",
            "$WEB2_SERVER www.$DOMAIN"
        )
        
        Add-Content $hostsPath $hostsEntries
        Write-Success "hosts文件配置完成"
        
        return $true
    }
    catch {
        Write-Error "hosts文件配置失败: $($_.Exception.Message)"
        return $false
    }
}

# 测试网络连通性
function Test-NetworkConnectivity {
    Write-Header "测试网络连通性..."
    
    $servers = @{
        "DNS服务器" = $DNS_SERVER
        "WEB服务器1" = $WEB1_SERVER
        "WEB服务器2" = $WEB2_SERVER
    }
    
    foreach ($server in $servers.GetEnumerator()) {
        try {
            $result = Test-Connection -ComputerName $server.Value -Count 3 -Quiet
            if ($result) {
                Write-Success "$($server.Key) ($($server.Value)) 连通正常"
            } else {
                Write-Error "$($server.Key) ($($server.Value)) 连通失败"
            }
        }
        catch {
            Write-Error "$($server.Key) ($($server.Value)) 测试失败: $($_.Exception.Message)"
        }
    }
}

# 测试DNS解析
function Test-DNSResolution {
    Write-Header "测试DNS解析..."
    
    $domains = @("dns.$DOMAIN", "web.$DOMAIN", "www.$DOMAIN")
    
    foreach ($domain in $domains) {
        try {
            $result = Resolve-DnsName -Name $domain -ErrorAction SilentlyContinue
            if ($result) {
                Write-Success "$domain 解析成功 -> $($result.IPAddress)"
            } else {
                Write-Error "$domain 解析失败"
            }
        }
        catch {
            Write-Error "$domain 解析失败: $($_.Exception.Message)"
        }
    }
}

# 测试HTTP服务
function Test-HTTPServices {
    Write-Header "测试HTTP服务..."
    
    # 测试WEB服务器1
    try {
        $response = Invoke-WebRequest -Uri "http://$WEB1_SERVER" -TimeoutSec 10 -ErrorAction SilentlyContinue
        if ($response.StatusCode -eq 200) {
            Write-Success "WEB服务器1 HTTP服务正常 (状态码: $($response.StatusCode))"
        } else {
            Write-Warning "WEB服务器1 HTTP服务异常 (状态码: $($response.StatusCode))"
        }
    }
    catch {
        Write-Error "WEB服务器1 HTTP服务测试失败: $($_.Exception.Message)"
    }
    
    # 测试WEB服务器2 HTTPS
    try {
        # 忽略SSL证书错误（自签名证书）
        [System.Net.ServicePointManager]::ServerCertificateValidationCallback = {$true}
        $response = Invoke-WebRequest -Uri "https://$WEB2_SERVER" -TimeoutSec 10 -ErrorAction SilentlyContinue
        if ($response.StatusCode -eq 200) {
            Write-Success "WEB服务器2 HTTPS服务正常 (状态码: $($response.StatusCode))"
        } else {
            Write-Warning "WEB服务器2 HTTPS服务异常 (状态码: $($response.StatusCode))"
        }
    }
    catch {
        Write-Error "WEB服务器2 HTTPS服务测试失败: $($_.Exception.Message)"
    }
}

# 显示访问信息
function Show-AccessInfo {
    Write-Header "服务访问信息"
    Write-Host ""
    Write-ColorOutput "🌐 Web服务器1 (Server02):" "Cyan"
    Write-Host "   - HTTP访问: http://web.$DOMAIN"
    Write-Host "   - IP访问: http://$WEB1_SERVER"
    Write-Host ""
    Write-ColorOutput "🔒 Web服务器2 (Server03):" "Cyan"
    Write-Host "   - HTTPS访问: https://www.$DOMAIN"
    Write-Host "   - IP访问: https://$WEB2_SERVER"
    Write-Host "   - 虚拟目录: https://www.$DOMAIN/xuni/"
    Write-Host ""
    Write-ColorOutput "🔍 DNS服务器 (Server01):" "Cyan"
    Write-Host "   - DNS服务器: $DNS_SERVER"
    Write-Host ""
}

# 生成配置报告
function New-ConfigReport {
    Write-Header "生成配置报告..."
    
    $reportFile = "windows_client_config_report_$(Get-Date -Format 'yyyyMMdd_HHmmss').txt"
    
    $reportContent = @"
Windows客户端DNS配置报告
生成时间: $(Get-Date)
操作系统: $((Get-WmiObject Win32_OperatingSystem).Caption)

服务器配置:
- DNS服务器: $DNS_SERVER
- WEB服务器1: $WEB1_SERVER
- WEB服务器2: $WEB2_SERVER
- 域名: $DOMAIN

配置项目:
1. DNS服务器配置
2. hosts文件配置
3. 网络连通性测试
4. DNS解析测试
5. HTTP服务测试

访问地址:
- WEB服务器1: http://web.$DOMAIN
- WEB服务器2: https://www.$DOMAIN
- 虚拟目录: https://www.$DOMAIN/xuni/

详细结果请查看PowerShell控制台输出。
"@
    
    Set-Content $reportFile $reportContent
    Write-Success "配置报告已生成: $reportFile"
}

# 主菜单
function Show-MainMenu {
    Write-Header "Windows客户端DNS配置工具"
    Write-Info "用于配置Windows客户端访问 $DOMAIN 服务"
    Write-Host ""
    
    Write-Host "请选择要执行的操作:"
    Write-Host "1) 配置DNS设置"
    Write-Host "2) 配置hosts文件"
    Write-Host "3) 测试网络连通性"
    Write-Host "4) 测试DNS解析"
    Write-Host "5) 测试HTTP服务"
    Write-Host "6) 显示访问信息"
    Write-Host "7) 生成配置报告"
    Write-Host "8) 全部配置和测试"
    Write-Host "0) 退出"
    Write-Host ""
    
    $choice = Read-Host "请输入选项 (0-8)"
    
    switch ($choice) {
        "1" { Set-DNSConfiguration }
        "2" { Set-HostsFile }
        "3" { Test-NetworkConnectivity }
        "4" { Test-DNSResolution }
        "5" { Test-HTTPServices }
        "6" { Show-AccessInfo }
        "7" { New-ConfigReport }
        "8" { 
            Set-DNSConfiguration
            Set-HostsFile
            Write-Host ""
            Write-Info "等待DNS配置生效..."
            Start-Sleep -Seconds 3
            Test-NetworkConnectivity
            Write-Host ""
            Test-DNSResolution
            Write-Host ""
            Test-HTTPServices
            Write-Host ""
            Show-AccessInfo
            New-ConfigReport
            Write-Success "Windows客户端配置完成!"
        }
        "0" { 
            Write-Info "退出配置工具"
            return
        }
        default { 
            Write-Error "无效选项，请重新选择"
        }
    }
    
    Write-Host ""
    Read-Host "按回车键继续..."
    Show-MainMenu
}

# 执行主函数
Show-MainMenu
