# 服务器配置脚本使用说明

## 概述

本套脚本用于配置三台服务器的网络服务：
- **Server01**: DNS服务器 (CentOS 9) - IP: ***************
- **Server02**: WEB服务器1 (Ubuntu) - IP: ***************
- **Server03**: WEB服务器2 (Ubuntu) - IP: ***************

## 脚本文件说明

### 1. server01_dns_config.sh
**功能**: 配置DNS服务器
- 安装和配置BIND DNS服务
- 配置域名解析 (mufeng.yuanchu)
- 设置防火墙规则
- 添加服务IP地址

### 2. server02_web_config.sh
**功能**: 配置WEB服务器1
- 安装Apache + PHP + MariaDB
- 配置WordPress环境
- 设置虚拟主机
- 端口80服务

### 3. server03_web_ssl_config.sh
**功能**: 配置WEB服务器2
- 安装Apache + SSL
- 配置HTTPS服务
- 设置虚拟目录
- HTTP自动重定向到HTTPS

### 4. test_all_servers.sh
**功能**: 测试所有服务器配置
- 网络连通性测试
- DNS解析测试
- WEB服务功能测试

### 5. config_check_and_fix.sh
**功能**: 配置检查和修复
- 检查网络配置
- 检查服务状态
- 修复常见问题
- 生成检查报告

### 6. 客户端配置脚本.sh
**功能**: 客户端DNS配置
- 自动配置客户端DNS设置
- 支持多种操作系统
- 网络连通性测试
- 交互式配置菜单

### 7. windows_client_config.ps1
**功能**: Windows客户端配置
- Windows PowerShell自动配置
- DNS和hosts文件配置
- 完整的测试验证功能
- 图形化配置指导

## 使用步骤

### 第一步：配置DNS服务器 (Server01)
```bash
# 在CentOS 9系统上执行
chmod +x server01_dns_config.sh
sudo ./server01_dns_config.sh
```

**配置内容**:
- DNS服务器IP: ***************
- 域名解析配置:
  - dns.mufeng.yuanchu -> ***************
  - web.mufeng.yuanchu -> ***************
  - www.mufeng.yuanchu -> ***************

### 第二步：配置WEB服务器1 (Server02)
```bash
# 在Ubuntu系统上执行
chmod +x server02_web_config.sh
sudo ./server02_web_config.sh
```

**配置内容**:
- Apache + PHP + MariaDB
- WordPress环境
- 访问地址: http://web.mufeng.yuanchu 或 http://***************

### 第三步：配置WEB服务器2 (Server03)
```bash
# 在Ubuntu系统上执行
chmod +x server03_web_ssl_config.sh
sudo ./server03_web_ssl_config.sh
```

**配置内容**:
- Apache + SSL
- HTTPS服务
- 虚拟目录: /xuni
- 访问地址: https://www.mufeng.yuanchu 或 https://***************

### 第四步：测试配置
```bash
# 在任意服务器上执行
chmod +x test_all_servers.sh
./test_all_servers.sh
```

### 第五步：检查和修复
```bash
# 在任意服务器上执行
chmod +x config_check_and_fix.sh
sudo ./config_check_and_fix.sh
```

### 第六步：配置客户端
```bash
# Linux客户端配置
chmod +x 客户端配置脚本.sh
sudo ./客户端配置脚本.sh

# Windows客户端配置（PowerShell管理员模式）
Set-ExecutionPolicy -ExecutionPolicy RemoteSigned -Scope CurrentUser
.\windows_client_config.ps1
```

## 重要配置说明

### 域名解析配置
- **主域名**: mufeng.yuanchu
- **DNS服务器**: ***************
- **子域名映射**:
  - dns.mufeng.yuanchu -> DNS服务器
  - web.mufeng.yuanchu -> WEB服务器1
  - www.mufeng.yuanchu -> WEB服务器2

### 网络配置
- 所有服务器在同一子网: *************/24
- 每台服务器会添加对应的服务IP地址
- 不会影响现有网络配置

### PHP配置
- 自动检测PHP版本
- 启用必要的PHP模块
- 配置Apache PHP支持

### SSL配置
- 使用自签名证书
- 强制HTTP重定向到HTTPS
- 支持虚拟目录访问

## 故障排除

### 常见问题及解决方案

#### 1. 网络连接问题
**症状**: 无法ping通服务器IP
**解决**:
- 检查网络接口配置
- 确认IP地址是否正确添加
- 检查防火墙规则

#### 2. DNS解析失败
**症状**: dig命令无法解析域名
**解决**:
- 检查BIND服务状态: `systemctl status named`
- 验证配置文件: `named-checkconf`
- 检查区域文件: `named-checkzone mufeng.yuanchu /var/named/mufeng.yuanchu.zone`

#### 3. WEB服务无法访问
**症状**: 浏览器无法打开网页
**解决**:
- 检查Apache状态: `systemctl status apache2`
- 验证配置文件: `apache2ctl configtest`
- 检查防火墙端口: `ufw status`

#### 4. PHP页面不显示
**症状**: PHP页面显示源代码
**解决**:
- 检查PHP模块: `apache2ctl -M | grep php`
- 重启Apache: `systemctl restart apache2`
- 检查PHP配置

#### 5. SSL证书问题
**症状**: HTTPS访问提示证书错误
**解决**:
- 这是正常现象（自签名证书）
- 浏览器选择"继续访问"
- 或安装证书到受信任根证书

### 日志文件位置
- **DNS日志**: `/var/log/messages` 或 `journalctl -u named`
- **Apache日志**: `/var/log/apache2/error.log`
- **系统日志**: `journalctl -xe`

### 配置文件位置
- **DNS配置**: `/etc/named.conf`
- **DNS区域文件**: `/var/named/mufeng.yuanchu.zone`
- **Apache配置**: `/etc/apache2/sites-available/`
- **SSL证书**: `/etc/apache2/ssl/`

## 客户端配置说明

### 支持的操作系统
- **Linux (Ubuntu/Debian)**: 自动配置systemd-resolved或resolv.conf
- **Linux (CentOS/RHEL)**: 使用NetworkManager或手动配置
- **Windows**: PowerShell脚本自动配置DNS和hosts文件
- **macOS**: 提供详细的手动配置指导

### 客户端配置功能
- **DNS自动配置**: 设置DNS服务器为***************
- **hosts文件配置**: 添加域名解析备用方案
- **网络测试**: 验证连通性和服务可用性
- **交互式菜单**: 提供多种配置选项

### Windows客户端特殊说明
1. 需要以管理员身份运行PowerShell
2. 可能需要修改执行策略：`Set-ExecutionPolicy RemoteSigned`
3. 自动检测网络适配器并配置DNS
4. 提供图形界面配置的详细步骤

## 测试验证

### DNS测试
```bash
# 测试DNS解析
dig @*************** dns.mufeng.yuanchu
dig @*************** web.mufeng.yuanchu
dig @*************** www.mufeng.yuanchu

# 客户端DNS测试
nslookup web.mufeng.yuanchu
nslookup www.mufeng.yuanchu
```

### WEB服务测试
```bash
# 测试HTTP服务
curl http://***************/
curl http://***************/index.php
curl http://web.mufeng.yuanchu/

# 测试HTTPS服务
curl -k https://***************/yuanchu.html
curl -k https://***************/xuni/
curl -k https://www.mufeng.yuanchu/
```

### 端口测试
```bash
# 测试端口连通性
nc -zv *************** 53    # DNS
nc -zv *************** 80    # HTTP
nc -zv *************** 443   # HTTPS
```

### 客户端访问测试
```bash
# 浏览器访问测试
# 打开浏览器访问以下地址：
# http://web.mufeng.yuanchu
# https://www.mufeng.yuanchu
# https://www.mufeng.yuanchu/xuni/
```

## 安全注意事项

1. **防火墙配置**: 脚本会自动配置防火墙规则
2. **SSL证书**: 使用自签名证书，生产环境建议使用正式证书
3. **数据库安全**: MariaDB root密码需要用户设置
4. **文件权限**: 脚本会自动设置正确的文件权限

## 技术支持

如遇到问题，请：
1. 运行 `config_check_and_fix.sh` 进行自动检查
2. 查看生成的报告文件
3. 检查相关日志文件
4. 参考故障排除部分

---

**注意**: 所有脚本需要root权限运行，请确保在执行前备份重要配置文件。
