#!/bin/bash
# 快速部署脚本
# 用于快速设置所有脚本的执行权限并提供部署指导

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[0;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 打印带颜色的信息
print_info() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_header() {
    echo -e "${BLUE}[HEADER]${NC} $1"
}

# 检查脚本文件
check_scripts() {
    print_header "检查脚本文件..."
    
    SCRIPTS=(
        "server01_dns_config.sh"
        "server02_web_config.sh" 
        "server03_web_ssl_config.sh"
        "test_all_servers.sh"
        "config_check_and_fix.sh"
    )
    
    MISSING_FILES=""
    
    for script in "${SCRIPTS[@]}"; do
        if [ -f "$script" ]; then
            print_success "找到脚本: $script"
        else
            print_error "缺少脚本: $script"
            MISSING_FILES="$MISSING_FILES $script"
        fi
    done
    
    if [ ! -z "$MISSING_FILES" ]; then
        print_error "缺少必要的脚本文件，请确保所有脚本文件都在当前目录"
        exit 1
    fi
}

# 设置执行权限
set_permissions() {
    print_header "设置脚本执行权限..."
    
    SCRIPTS=(
        "server01_dns_config.sh"
        "server02_web_config.sh"
        "server03_web_ssl_config.sh" 
        "test_all_servers.sh"
        "config_check_and_fix.sh"
        "quick_deploy.sh"
    )
    
    for script in "${SCRIPTS[@]}"; do
        if [ -f "$script" ]; then
            chmod +x "$script"
            print_success "已设置执行权限: $script"
        fi
    done
}

# 检查系统类型
detect_system() {
    print_header "检测系统类型..."
    
    if [ -f /etc/os-release ]; then
        . /etc/os-release
        OS=$NAME
        VER=$VERSION_ID
        print_info "检测到系统: $OS $VER"
        
        if [[ "$OS" == *"CentOS"* ]] || [[ "$OS" == *"Red Hat"* ]] || [[ "$OS" == *"Rocky"* ]]; then
            SYSTEM_TYPE="centos"
            print_info "系统类型: CentOS/RHEL"
        elif [[ "$OS" == *"Ubuntu"* ]] || [[ "$OS" == *"Debian"* ]]; then
            SYSTEM_TYPE="ubuntu"
            print_info "系统类型: Ubuntu/Debian"
        else
            SYSTEM_TYPE="unknown"
            print_warning "未知系统类型，可能需要手动调整"
        fi
    else
        print_warning "无法检测系统类型"
        SYSTEM_TYPE="unknown"
    fi
}

# 显示部署指导
show_deployment_guide() {
    print_header "部署指导"
    
    echo ""
    echo "=== 服务器配置部署指导 ==="
    echo ""
    
    print_info "配置概览:"
    echo "  - Server01 (DNS服务器): *************** - CentOS 9"
    echo "  - Server02 (WEB服务器1): *************** - Ubuntu"  
    echo "  - Server03 (WEB服务器2): *************** - Ubuntu"
    echo "  - 域名: mufeng.yuanchu"
    echo ""
    
    print_info "部署步骤:"
    echo ""
    
    print_success "第一步: 配置DNS服务器 (Server01 - CentOS 9)"
    echo "  在IP为***************的CentOS 9服务器上执行:"
    echo "  sudo ./server01_dns_config.sh"
    echo ""
    
    print_success "第二步: 配置WEB服务器1 (Server02 - Ubuntu)"
    echo "  在IP为***************的Ubuntu服务器上执行:"
    echo "  sudo ./server02_web_config.sh"
    echo ""
    
    print_success "第三步: 配置WEB服务器2 (Server03 - Ubuntu)"
    echo "  在IP为***************的Ubuntu服务器上执行:"
    echo "  sudo ./server03_web_ssl_config.sh"
    echo ""
    
    print_success "第四步: 测试所有服务器配置"
    echo "  在任意服务器上执行:"
    echo "  ./test_all_servers.sh"
    echo ""
    
    print_success "第五步: 检查和修复配置"
    echo "  在任意服务器上执行:"
    echo "  sudo ./config_check_and_fix.sh"
    echo ""
    
    print_warning "注意事项:"
    echo "  1. 所有配置脚本需要root权限运行"
    echo "  2. 确保网络连接正常"
    echo "  3. 建议按顺序执行配置"
    echo "  4. 每步完成后进行测试验证"
    echo ""
    
    print_info "访问地址:"
    echo "  - DNS服务器: ***************:53"
    echo "  - WEB服务器1: http://web.mufeng.yuanchu 或 http://***************"
    echo "  - WEB服务器2: https://www.mufeng.yuanchu 或 https://***************"
    echo "  - 虚拟目录: https://www.mufeng.yuanchu/xuni/"
    echo ""
}

# 检查依赖工具
check_dependencies() {
    print_header "检查依赖工具..."
    
    TOOLS=("curl" "wget" "dig" "nc")
    MISSING_TOOLS=""
    
    for tool in "${TOOLS[@]}"; do
        if command -v $tool >/dev/null 2>&1; then
            print_success "工具可用: $tool"
        else
            print_warning "工具缺失: $tool"
            MISSING_TOOLS="$MISSING_TOOLS $tool"
        fi
    done
    
    if [ ! -z "$MISSING_TOOLS" ]; then
        print_warning "缺少以下工具: $MISSING_TOOLS"
        print_info "安装建议:"
        
        if [ "$SYSTEM_TYPE" = "ubuntu" ]; then
            echo "  Ubuntu/Debian: sudo apt update && sudo apt install -y curl wget dnsutils netcat-openbsd"
        elif [ "$SYSTEM_TYPE" = "centos" ]; then
            echo "  CentOS/RHEL: sudo dnf install -y curl wget bind-utils nmap-ncat"
        else
            echo "  请根据系统类型安装相应的工具包"
        fi
        echo ""
    fi
}

# 创建部署日志
create_deployment_log() {
    print_header "创建部署日志..."
    
    LOG_FILE="deployment_$(date +%Y%m%d_%H%M%S).log"
    
    cat > $LOG_FILE << EOF
服务器配置部署日志
创建时间: $(date)
系统信息: $(uname -a)

部署配置:
- DNS服务器: *************** (CentOS 9)
- WEB服务器1: *************** (Ubuntu)
- WEB服务器2: *************** (Ubuntu)
- 域名: mufeng.yuanchu

脚本文件:
- server01_dns_config.sh (DNS服务器配置)
- server02_web_config.sh (WEB服务器1配置)
- server03_web_ssl_config.sh (WEB服务器2配置)
- test_all_servers.sh (测试脚本)
- config_check_and_fix.sh (检查修复脚本)

部署状态: 准备就绪
下一步: 按照部署指导执行配置脚本

注意: 请确保在正确的服务器上执行对应的配置脚本
EOF
    
    print_success "部署日志已创建: $LOG_FILE"
}

# 交互式部署助手
interactive_deployment() {
    print_header "交互式部署助手"
    
    echo ""
    echo "请选择要执行的操作:"
    echo "1) 显示当前系统信息"
    echo "2) 检查网络配置"
    echo "3) 根据系统类型推荐配置脚本"
    echo "4) 显示完整部署指导"
    echo "5) 退出"
    echo ""
    
    read -p "请输入选项 (1-5): " choice
    
    case $choice in
        1)
            print_info "当前系统信息:"
            echo "  主机名: $(hostname)"
            echo "  系统: $(uname -s)"
            echo "  内核: $(uname -r)"
            echo "  架构: $(uname -m)"
            if [ -f /etc/os-release ]; then
                echo "  发行版: $(grep PRETTY_NAME /etc/os-release | cut -d'"' -f2)"
            fi
            echo "  IP地址: $(ip route get 1 | awk '{print $7}' | head -1)"
            ;;
        2)
            print_info "网络配置检查:"
            echo "  默认网关: $(ip route | grep default | awk '{print $3}')"
            echo "  网络接口: $(ip -o -4 route show to default | awk '{print $5}')"
            echo "  当前IP: $(ip route get 1 | awk '{print $7}' | head -1)"
            ;;
        3)
            CURRENT_IP=$(ip route get 1 | awk '{print $7}' | head -1)
            print_info "根据当前IP ($CURRENT_IP) 推荐配置:"
            
            if [[ "$CURRENT_IP" == "***************" ]]; then
                print_success "当前服务器应配置为DNS服务器"
                echo "  执行: sudo ./server01_dns_config.sh"
            elif [[ "$CURRENT_IP" == "***************" ]]; then
                print_success "当前服务器应配置为WEB服务器1"
                echo "  执行: sudo ./server02_web_config.sh"
            elif [[ "$CURRENT_IP" == "***************" ]]; then
                print_success "当前服务器应配置为WEB服务器2"
                echo "  执行: sudo ./server03_web_ssl_config.sh"
            else
                print_warning "当前IP不在预期范围内，请手动选择配置脚本"
            fi
            ;;
        4)
            show_deployment_guide
            ;;
        5)
            print_info "退出部署助手"
            exit 0
            ;;
        *)
            print_error "无效选项，请重新选择"
            interactive_deployment
            ;;
    esac
    
    echo ""
    read -p "按回车键继续..." 
    interactive_deployment
}

# 主函数
main() {
    print_header "服务器配置快速部署工具"
    echo ""
    
    # 执行检查
    check_scripts
    set_permissions
    detect_system
    check_dependencies
    create_deployment_log
    
    echo ""
    print_success "准备工作完成!"
    echo ""
    
    # 显示部署指导
    show_deployment_guide
    
    # 询问是否启动交互式助手
    read -p "是否启动交互式部署助手? (y/n): " start_interactive
    if [[ "$start_interactive" =~ ^[Yy]$ ]]; then
        interactive_deployment
    fi
    
    print_info "快速部署工具执行完成"
    print_info "请参考 使用说明.md 获取详细信息"
}

# 执行主函数
main
