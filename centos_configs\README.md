# CentOS系统配置脚本集合

## 概述

本文件夹包含专门为CentOS系统设计的服务器配置脚本，所有三台服务器都使用CentOS操作系统。

## 系统架构

### 服务器配置
- **Server01 (DNS服务器)**: CentOS 9 - IP: ***************
- **Server02 (WEB服务器1)**: CentOS 9 - IP: ***************  
- **Server03 (WEB服务器2)**: CentOS 9 - IP: ***************

### 服务配置
- **DNS服务器**: BIND DNS服务，提供域名解析
- **WEB服务器1**: Apache + PHP + MariaDB + WordPress (HTTP)
- **WEB服务器2**: Apache + SSL + 虚拟目录 (HTTPS)

## 脚本文件说明

### 1. centos_server01_dns_config.sh
**功能**: CentOS DNS服务器配置
- 安装和配置BIND DNS服务
- 配置域名解析 (mufeng.yuanchu)
- 设置firewalld防火墙规则
- 使用dnf包管理器

### 2. centos_server02_web_config.sh  
**功能**: CentOS WEB服务器1配置
- 安装Apache + PHP + MariaDB
- 配置WordPress环境
- 使用dnf安装软件包
- 配置firewalld防火墙

### 3. centos_server03_web_ssl_config.sh
**功能**: CentOS WEB服务器2配置
- 安装Apache + SSL模块
- 配置HTTPS服务和虚拟目录
- 生成自签名SSL证书
- 配置HTTP到HTTPS重定向

### 4. centos_test_all_servers.sh
**功能**: CentOS系统测试脚本
- 测试所有服务器功能
- 验证DNS解析和WEB服务
- CentOS特定的测试命令

### 5. centos_client_config.sh
**功能**: CentOS客户端配置
- 使用NetworkManager配置DNS
- CentOS特定的网络配置
- firewalld防火墙配置

## CentOS特定配置

### 包管理器
- 使用 `dnf` 替代 `apt`
- 配置CentOS Stream软件源
- EPEL仓库支持

### 防火墙配置
- 使用 `firewalld` 替代 `ufw`
- 配置服务和端口规则
- 永久性防火墙配置

### 服务管理
- 使用 `systemctl` 管理服务
- 配置服务自启动
- 服务状态检查

### 网络配置
- 使用 `NetworkManager` 配置网络
- `nmcli` 命令行工具
- 网络接口配置

## 使用步骤

### 第一步：配置DNS服务器 (Server01)
```bash
chmod +x centos_server01_dns_config.sh
sudo ./centos_server01_dns_config.sh
```

### 第二步：配置WEB服务器1 (Server02)
```bash
chmod +x centos_server02_web_config.sh
sudo ./centos_server02_web_config.sh
```

### 第三步：配置WEB服务器2 (Server03)
```bash
chmod +x centos_server03_web_ssl_config.sh
sudo ./centos_server03_web_ssl_config.sh
```

### 第四步：测试所有服务器
```bash
chmod +x centos_test_all_servers.sh
./centos_test_all_servers.sh
```

### 第五步：配置客户端
```bash
chmod +x centos_client_config.sh
sudo ./centos_client_config.sh
```

## CentOS版本兼容性

### 支持的版本
- **CentOS Stream 9**: 主要支持版本
- **CentOS Stream 8**: 兼容支持
- **RHEL 9**: 完全兼容
- **Rocky Linux 9**: 完全兼容
- **AlmaLinux 9**: 完全兼容

### 软件版本
- **BIND**: 9.16+
- **Apache**: 2.4+
- **PHP**: 8.0+
- **MariaDB**: 10.5+

## 与Ubuntu版本的差异

### 主要差异
1. **包管理器**: dnf vs apt
2. **防火墙**: firewalld vs ufw
3. **服务配置**: 不同的配置文件路径
4. **网络管理**: NetworkManager vs systemd-networkd

### 配置路径差异
- **Apache配置**: `/etc/httpd/` vs `/etc/apache2/`
- **PHP配置**: `/etc/php.ini` vs `/etc/php/*/`
- **SSL证书**: `/etc/pki/tls/` vs `/etc/ssl/`

## 注意事项

1. **SELinux**: CentOS默认启用SELinux，脚本包含相关配置
2. **防火墙**: 使用firewalld，配置方式与ufw不同
3. **软件源**: 可能需要配置EPEL仓库
4. **权限**: 某些配置需要特殊的SELinux权限

## 故障排除

### 常见问题
1. **软件源问题**: 检查网络连接和软件源配置
2. **SELinux问题**: 检查SELinux策略和上下文
3. **防火墙问题**: 验证firewalld规则配置
4. **服务启动问题**: 检查systemctl状态和日志

### 日志位置
- **系统日志**: `/var/log/messages`
- **Apache日志**: `/var/log/httpd/`
- **DNS日志**: `/var/log/named/`
- **防火墙日志**: `journalctl -u firewalld`

---

**注意**: 本配置脚本专门为CentOS系统优化，确保在CentOS环境中获得最佳的兼容性和性能。
