#!/bin/bash
# Server01 - DNS服务器完整配置脚本
# 系统: CentOS 9
# 功能: BIND DNS服务器，提供域名解析服务
# 包含错误检测和自动修复功能

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[0;33m'
NC='\033[0m' # No Color

# 固定IP配置
SERVER_IP="***************"
SUBNET_MASK="24"
WEB1_IP="***************"
WEB2_IP="***************"

# 打印带颜色的信息
print_info() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

# 检查是否以root用户运行
if [ "$EUID" -ne 0 ]; then
    print_error "请以root用户运行此脚本"
    exit 1
fi

# 显示固定的服务IP配置
show_config() {
    print_info "DNS服务器固定IP配置："
    print_info "DNS服务器IP: $SERVER_IP/$SUBNET_MASK"
    print_info "WEB服务器1 IP: $WEB1_IP"
    print_info "WEB服务器2 IP: $WEB2_IP"
    print_info "域名: mufeng.yuanchu"

    read -p "确认使用以上固定配置? (y/n): " confirm
    if [[ ! "$confirm" =~ ^[Yy]$ ]]; then
        print_error "用户取消配置"
        exit 1
    fi
}

# 添加服务IP地址
setup_ip() {
    print_info "正在为DNS服务添加IP地址 $SERVER_IP/$SUBNET_MASK..."

    # 获取网络接口名称
    INTERFACE=$(ip -o -4 route show to default | awk '{print $5}')

    if [ -z "$INTERFACE" ]; then
        print_error "无法确定网络接口"
        exit 1
    fi

    print_info "在网络接口 $INTERFACE 上添加服务IP地址..."

    # 检查IP是否已经存在
    if ip addr show $INTERFACE | grep -q "$SERVER_IP"; then
        print_warning "IP地址 $SERVER_IP 已经存在于接口 $INTERFACE 上"
        return 0
    fi

    # 添加IP地址到现有接口（不删除原有配置）
    if ip addr add $SERVER_IP/$SUBNET_MASK dev $INTERFACE 2>/dev/null; then
        print_info "成功添加IP地址 $SERVER_IP 到接口 $INTERFACE"
    else
        # 如果添加失败，可能是因为IP已存在，检查是否真的存在
        if ip addr show $INTERFACE | grep -q "$SERVER_IP"; then
            print_warning "IP地址 $SERVER_IP 已经存在，跳过添加"
        else
            print_error "添加IP地址失败"
            exit 1
        fi
    fi

    # 创建持久化配置（CentOS 9方式）
    print_info "创建持久化IP配置..."

    # 创建网络脚本来在启动时添加IP
    cat > /etc/systemd/system/dns-service-ip.service << EOF
[Unit]
Description=Add DNS Service IP Address
After=network.target

[Service]
Type=oneshot
ExecStart=/bin/ip addr add $SERVER_IP/$SUBNET_MASK dev $INTERFACE
RemainAfterExit=yes
StandardOutput=journal

[Install]
WantedBy=multi-user.target
EOF

    # 启用服务
    systemctl enable dns-service-ip.service

    # 验证IP配置
    sleep 2
    print_info "当前接口 $INTERFACE 的IP配置："
    ip addr show $INTERFACE | grep inet

    print_info "服务IP地址添加完成，已配置开机自启"
}

# 修复网络连接
fix_network() {
    print_info "正在修复网络连接..."

    # 临时修复DNS
    echo "nameserver *******" > /etc/resolv.conf
    echo "nameserver *******" >> /etc/resolv.conf

    # 测试网络连接
    if ping -c 3 ******* >/dev/null 2>&1; then
        print_info "网络连接正常"
    else
        print_warning "网络连接异常，尝试重启网络服务..."
        systemctl restart systemd-networkd
        systemctl restart systemd-resolved
        sleep 5
    fi
}

# 修复和配置软件源
fix_and_update_sources() {
    print_info "正在检查和修复软件源..."

    # 备份原始repo文件
    if [ ! -d "/etc/yum.repos.d.backup" ]; then
        cp -r /etc/yum.repos.d /etc/yum.repos.d.backup 2>/dev/null || true
    fi

    # 完全清理现有配置
    print_info "清理现有软件源配置..."
    rm -f /etc/yum.repos.d/*.repo

    # 创建单一的软件源配置文件
    print_info "创建新的软件源配置..."
    cat > /etc/yum.repos.d/centos9.repo << 'EOF'
[baseos]
name=CentOS Stream 9 - BaseOS
baseurl=http://mirror.stream.centos.org/9-stream/BaseOS/x86_64/os/
gpgcheck=1
enabled=1
gpgkey=file:///etc/pki/rpm-gpg/RPM-GPG-KEY-centosofficial

[appstream]
name=CentOS Stream 9 - AppStream
baseurl=http://mirror.stream.centos.org/9-stream/AppStream/x86_64/os/
gpgcheck=1
enabled=1
gpgkey=file:///etc/pki/rpm-gpg/RPM-GPG-KEY-centosofficial
EOF

    # 清理缓存
    print_info "清理软件包缓存..."
    dnf clean all

    # 重建缓存
    print_info "重建软件包缓存..."
    if ! dnf makecache; then
        print_warning "官方源失败，尝试阿里云镜像..."

        # 备用方案：使用阿里云镜像
        cat > /etc/yum.repos.d/centos9.repo << 'EOF'
[baseos]
name=CentOS Stream 9 - BaseOS - Aliyun
baseurl=https://mirrors.aliyun.com/centos-stream/9-stream/BaseOS/x86_64/os/
gpgcheck=1
enabled=1
gpgkey=file:///etc/pki/rpm-gpg/RPM-GPG-KEY-centosofficial

[appstream]
name=CentOS Stream 9 - AppStream - Aliyun
baseurl=https://mirrors.aliyun.com/centos-stream/9-stream/AppStream/x86_64/os/
gpgcheck=1
enabled=1
gpgkey=file:///etc/pki/rpm-gpg/RPM-GPG-KEY-centosofficial
EOF

        dnf clean all
        if ! dnf makecache; then
            print_warning "阿里云源也失败，尝试恢复原始配置..."

            # 恢复原始配置
            if [ -d "/etc/yum.repos.d.backup" ]; then
                rm -f /etc/yum.repos.d/*.repo
                cp /etc/yum.repos.d.backup/*.repo /etc/yum.repos.d/ 2>/dev/null || true
                dnf clean all
                if dnf makecache; then
                    print_info "已恢复原始软件源配置"
                else
                    print_error "所有软件源配置都失败，请检查网络连接"
                    return 1
                fi
            else
                print_error "无法恢复原始配置，请检查网络连接"
                return 1
            fi
        fi
    fi

    print_info "软件源配置成功"
    return 0
}

# 安装BIND DNS服务
install_bind() {
    print_info "正在安装BIND DNS服务..."

    # 检查是否已安装
    if command -v named >/dev/null 2>&1; then
        print_info "BIND已经安装"
        return 0
    fi

    # 检查网络连接
    if ! ping -c 1 ******* >/dev/null 2>&1; then
        print_warning "网络连接异常，请检查网络设置"
    fi

    # 直接尝试安装最常见的包组合
    print_info "尝试安装BIND DNS服务包..."

    # 尝试安装bind包
    if dnf install -y bind bind-utils 2>/dev/null; then
        print_info "BIND安装成功"
        return 0
    fi

    # 如果bind不可用，尝试其他包名
    print_info "尝试其他包名..."
    local packages=("named" "bind9" "dns-server")

    for pkg in "${packages[@]}"; do
        print_info "尝试安装: $pkg"
        if dnf install -y $pkg bind-utils 2>/dev/null; then
            print_info "$pkg 安装成功"
            return 0
        fi
    done

    # 如果所有标准包都失败，尝试组安装
    print_info "尝试安装DNS服务器组..."
    if dnf groupinstall -y "DNS Name Server" 2>/dev/null; then
        print_info "DNS服务器组安装成功"
        return 0
    fi

    # 最后尝试搜索和安装
    print_warning "搜索可用的DNS相关包..."
    local dns_packages=$(dnf search dns server 2>/dev/null | grep -i "bind\|named\|dns.*server" | head -3 | awk '{print $1}' | cut -d. -f1)

    if [ ! -z "$dns_packages" ]; then
        print_info "找到DNS包: $dns_packages"
        for pkg in $dns_packages; do
            if dnf install -y $pkg 2>/dev/null; then
                print_info "$pkg 安装成功"
                break
            fi
        done
    fi

    # 验证安装结果
    if command -v named >/dev/null 2>&1; then
        print_info "BIND DNS服务安装完成"
        return 0
    else
        # 最后的备用方案：安装dnsmasq
        print_warning "BIND安装失败，尝试安装dnsmasq作为备用DNS服务..."
        if dnf install -y dnsmasq 2>/dev/null; then
            print_warning "已安装dnsmasq作为备用DNS服务"
            print_warning "注意: 需要手动配置dnsmasq，或重新尝试安装BIND"
            return 0
        else
            print_error "所有DNS服务安装都失败"
            return 1
        fi
    fi
}

# 配置BIND
configure_bind() {
    print_info "正在配置BIND DNS服务..."

    # 备份原始配置
    cp /etc/named.conf /etc/named.conf.bak 2>/dev/null || true

    # 计算网络地址
    NETWORK=$(echo $SERVER_IP | cut -d. -f1-3).0/$SUBNET_MASK

    # 修改主配置文件
    cat > /etc/named.conf << EOF
//
// named.conf
//
// Provided by Red Hat bind package to configure the ISC BIND named(8) DNS
// server as a caching only nameserver (as a localhost DNS resolver only).
//
// See /usr/share/doc/bind*/sample/ for example named configuration files.
//

options {
    listen-on port 53 { 127.0.0.1; $SERVER_IP; any; };
    listen-on-v6 port 53 { ::1; };
    directory "/var/named";
    dump-file "/var/named/data/cache_dump.db";
    statistics-file "/var/named/data/named_stats.txt";
    memstatistics-file "/var/named/data/named_mem_stats.txt";
    secroots-file "/var/named/data/named.secroots";
    recursing-file "/var/named/data/named.recursing";
    allow-query { localhost; any; };

    recursion yes;
    dnssec-validation yes;

    managed-keys-directory "/var/named/dynamic";
    geoip-directory "/usr/share/GeoIP";

    pid-file "/run/named/named.pid";
    session-keyfile "/run/named/session.key";

    /* https://fedoraproject.org/wiki/Changes/CryptoPolicy */
    include "/etc/crypto-policies/back-ends/bind.config";

    forwarders {
        *******;
        *******;
    };
};

logging {
    channel default_debug {
        file "data/named.run";
        severity dynamic;
    };
};

zone "." IN {
    type hint;
    file "named.ca";
};

zone "mufeng.yuanchu" IN {
    type master;
    file "mufeng.yuanchu.zone";
    allow-update { none; };
};

include "/etc/named.rfc1912.zones";
include "/etc/named.root.key";
EOF

    # 创建区域文件
    cat > /var/named/mufeng.yuanchu.zone << EOF
\$TTL 86400
@   IN  SOA     dns.mufeng.yuanchu. admin.mufeng.yuanchu. (
        2024062001  ; Serial
        3600        ; Refresh
        1800        ; Retry
        604800      ; Expire
        86400 )     ; Minimum TTL
;
@       IN  NS      dns.mufeng.yuanchu.
dns     IN  A       $SERVER_IP
web     IN  A       $WEB1_IP
www     IN  A       $WEB2_IP
EOF

    # 设置权限
    chown named:named /var/named/mufeng.yuanchu.zone
    chmod 644 /var/named/mufeng.yuanchu.zone

    print_info "BIND DNS服务配置完成"
}

# 配置防火墙
configure_firewall() {
    print_info "正在配置防火墙..."

    # 检查firewalld状态
    if ! systemctl is-active --quiet firewalld; then
        systemctl enable firewalld
        systemctl start firewalld
    fi

    # 添加DNS服务到防火墙
    firewall-cmd --permanent --add-service=dns
    firewall-cmd --permanent --add-port=53/tcp
    firewall-cmd --permanent --add-port=53/udp

    # 重新加载防火墙配置
    firewall-cmd --reload

    print_info "防火墙配置完成"
}

# 启动DNS服务
start_dns() {
    print_info "正在启动DNS服务..."

    # 检查配置
    named-checkconf
    named-checkzone mufeng.yuanchu /var/named/mufeng.yuanchu.zone

    # 启动服务
    systemctl enable named
    systemctl start named

    # 检查服务状态
    if systemctl is-active --quiet named; then
        print_info "DNS服务启动成功"
    else
        print_error "DNS服务启动失败"
        exit 1
    fi
}

# 测试DNS配置
test_dns() {
    print_info "正在测试DNS配置..."

    # 等待DNS服务完全启动
    sleep 5

    # 测试DNS解析
    print_info "测试DNS解析..."
    sleep 5  # 等待DNS服务完全启动

    # 测试本地解析
    if dig @127.0.0.1 dns.mufeng.yuanchu +short | grep -q "$SERVER_IP"; then
        print_success "dns.mufeng.yuanchu 解析正常"
    else
        print_warning "dns.mufeng.yuanchu 解析异常"
    fi

    if dig @127.0.0.1 web.mufeng.yuanchu +short | grep -q "$WEB1_IP"; then
        print_success "web.mufeng.yuanchu 解析正常"
    else
        print_warning "web.mufeng.yuanchu 解析异常"
    fi

    if dig @127.0.0.1 www.mufeng.yuanchu +short | grep -q "$WEB2_IP"; then
        print_success "www.mufeng.yuanchu 解析正常"
    else
        print_warning "www.mufeng.yuanchu 解析异常"
    fi

    print_info "DNS测试完成"
    print_info "请确保客户端DNS设置为$SERVER_IP"

    # 添加本地hosts条目以便测试
    print_info "添加本地hosts条目..."
    if ! grep -q "mufeng.yuanchu" /etc/hosts; then
        cat >> /etc/hosts << EOF

# Mufeng.yuanchu 域名解析
$SERVER_IP dns.mufeng.yuanchu
$WEB1_IP web.mufeng.yuanchu
$WEB2_IP www.mufeng.yuanchu
EOF
        print_info "已添加hosts条目"
    else
        print_info "hosts条目已存在"
    fi
}

# 主函数
main() {
    print_info "开始配置Server01 - DNS服务器 (CentOS 9)..."

    # 显示固定IP配置
    show_config

    # 询问是否添加服务IP地址
    read -p "是否为DNS服务添加IP地址$SERVER_IP/$SUBNET_MASK? (不会影响现有网络配置) (y/n): " answer
    if [[ "$answer" =~ ^[Yy]$ ]]; then
        setup_ip
    fi

    # 修复网络和安装DNS服务
    fix_network

    # 修复和配置软件源
    if ! fix_and_update_sources; then
        print_error "软件源配置失败，无法继续"
        exit 1
    fi

    # 安装和配置DNS服务
    if ! install_bind; then
        print_error "BIND安装失败，无法继续"
        exit 1
    fi

    configure_bind
    configure_firewall
    start_dns
    test_dns

    print_info "Server01 - DNS服务器配置完成!"
    print_info "DNS服务器地址: $SERVER_IP"
    print_info "支持的域名解析:"
    print_info "  - dns.mufeng.yuanchu -> $SERVER_IP"
    print_info "  - web.mufeng.yuanchu -> $WEB1_IP"
    print_info "  - www.mufeng.yuanchu -> $WEB2_IP"
}

# 执行主函数
main
