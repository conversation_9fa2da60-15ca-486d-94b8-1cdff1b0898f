#!/bin/bash
# Server02 - WEB服务器1配置脚本
# 系统: Ubuntu
# 功能: Apache + MariaDB + WordPress，端口80

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[0;33m'
NC='\033[0m' # No Color

# 全局变量
SERVER_IP=""
SUBNET_MASK=""

# 打印带颜色的信息
print_info() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

# 检查是否以root用户运行
if [ "$EUID" -ne 0 ]; then
    print_error "请以root用户运行此脚本"
    exit 1
fi

# 设置固定的服务IP配置
get_ip_config() {
    print_info "WEB服务器1固定IP配置："

    # 固定IP分配
    SERVER_IP="***************"
    SUBNET_MASK="24"

    print_info "WEB服务器1 IP: $SERVER_IP/$SUBNET_MASK"
    print_info "域名: web.mufeng.yuanchu"

    read -p "确认使用以上固定配置? (y/n): " confirm
    if [[ ! "$confirm" =~ ^[Yy]$ ]]; then
        print_error "用户取消配置"
        exit 1
    fi
}

# 添加服务IP地址
setup_ip() {
    print_info "正在为WEB服务添加IP地址 $SERVER_IP/$SUBNET_MASK..."

    # 获取网络接口名称
    INTERFACE=$(ip -o -4 route show to default | awk '{print $5}')

    if [ -z "$INTERFACE" ]; then
        print_error "无法确定网络接口"
        exit 1
    fi

    print_info "在网络接口 $INTERFACE 上添加服务IP地址..."

    # 检查IP是否已经存在
    if ip addr show $INTERFACE | grep -q "$SERVER_IP"; then
        print_warning "IP地址 $SERVER_IP 已经存在于接口 $INTERFACE 上"
        return 0
    fi

    # 添加IP地址到现有接口（不删除原有配置）
    if ip addr add $SERVER_IP/$SUBNET_MASK dev $INTERFACE 2>/dev/null; then
        print_info "成功添加IP地址 $SERVER_IP 到接口 $INTERFACE"
    else
        # 如果添加失败，可能是因为IP已存在，检查是否真的存在
        if ip addr show $INTERFACE | grep -q "$SERVER_IP"; then
            print_warning "IP地址 $SERVER_IP 已经存在，跳过添加"
        else
            print_error "添加IP地址失败"
            exit 1
        fi
    fi

    # 创建持久化配置（Ubuntu方式）
    print_info "创建持久化IP配置..."

    # 创建网络脚本来在启动时添加IP
    cat > /etc/systemd/system/web1-service-ip.service << EOF
[Unit]
Description=Add Web1 Service IP Address
After=network.target

[Service]
Type=oneshot
ExecStart=/bin/ip addr add $SERVER_IP/$SUBNET_MASK dev $INTERFACE
RemainAfterExit=yes
StandardOutput=journal

[Install]
WantedBy=multi-user.target
EOF

    # 启用服务
    systemctl enable web1-service-ip.service

    # 验证IP配置
    sleep 2
    print_info "当前接口 $INTERFACE 的IP配置："
    ip addr show $INTERFACE | grep inet

    print_info "服务IP地址添加完成，已配置开机自启"
}

# 安装Apache和PHP
install_apache_php() {
    print_info "正在安装Apache和PHP..."

    # 更新系统
    apt update -y

    # 安装Apache和PHP (Ubuntu方式)
    if ! apt install -y apache2 php libapache2-mod-php php-mysql php-json php-gd php-mbstring php-xml php-zip php-curl; then
        print_error "Apache和PHP安装失败，请检查网络连接或软件源配置"
        exit 1
    fi

    # 启用Apache模块
    a2enmod rewrite 2>/dev/null || true
    # 自动检测PHP版本并启用对应模块
    PHP_VERSION=$(php -v | head -n1 | cut -d' ' -f2 | cut -d'.' -f1,2)
    a2enmod php$PHP_VERSION 2>/dev/null || a2enmod php8.1 2>/dev/null || a2enmod php8.0 2>/dev/null || a2enmod php7.4 2>/dev/null || true

    print_info "Apache和PHP安装完成"
}

# 配置Apache
configure_apache() {
    print_info "正在配置Apache..."

    # 创建网站目录
    mkdir -p /website

    # 配置虚拟主机 (Ubuntu Apache2)
    cat > /etc/apache2/sites-available/website.conf << EOF
<VirtualHost *:80>
    ServerName web.mufeng.yuanchu
    ServerAlias $SERVER_IP
    ServerAlias localhost
    DocumentRoot /website
    DirectoryIndex index.html index.php

    <Directory /website>
        AllowOverride All
        Require all granted
        Options Indexes FollowSymLinks
    </Directory>

    # 添加MIME类型支持
    <FilesMatch "\.php$">
        SetHandler application/x-httpd-php
    </FilesMatch>

    ErrorLog \${APACHE_LOG_DIR}/website-error.log
    CustomLog \${APACHE_LOG_DIR}/website-access.log combined
</VirtualHost>
EOF

    # 启用站点
    a2ensite website.conf
    a2dissite 000-default.conf 2>/dev/null || true

    # 确保PHP模块已启用
    print_info "启用PHP模块..."
    a2enmod php8.1 2>/dev/null || a2enmod php8.0 2>/dev/null || a2enmod php7.4 2>/dev/null || true

    # 启用rewrite模块
    a2enmod rewrite 2>/dev/null || true

    # 创建主页面
    cat > /website/index.html << 'EOF'
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Welcome to Web Server 1</title>
    <style>
        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            margin: 0;
            padding: 40px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
            overflow: hidden;
        }
        .header {
            background: linear-gradient(135deg, #2c3e50 0%, #34495e 100%);
            color: white;
            padding: 30px;
            text-align: center;
        }
        .header h1 {
            margin: 0;
            font-size: 2.5em;
            font-weight: 300;
        }
        .info {
            padding: 30px;
        }
        .info-card {
            background: #f8f9fa;
            padding: 20px;
            border-radius: 10px;
            border-left: 4px solid #3498db;
            margin: 20px 0;
        }
        .links a {
            display: inline-block;
            background: #3498db;
            color: white;
            padding: 12px 24px;
            text-decoration: none;
            border-radius: 25px;
            margin: 5px 10px 5px 0;
            transition: all 0.3s ease;
        }
        .links a:hover {
            background: #2980b9;
            transform: translateY(-2px);
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>Welcome to Web Server 1</h1>
            <p>Server02 - Apache + MariaDB + WordPress</p>
        </div>

        <div class="info">
            <div class="info-card">
                <h3>服务器信息</h3>
                <p><strong>IP地址:</strong> ***************</p>
                <p><strong>域名:</strong> web.mufeng.yuanchu</p>
                <p><strong>服务:</strong> Apache + MariaDB + WordPress</p>
                <p><strong>端口:</strong> 80</p>
                <p><strong>状态:</strong> 正常运行</p>
            </div>

            <div class="links">
                <h3>功能测试</h3>
                <a href="index.php">PHP信息页面</a>
                <a href="test.php">PHP测试</a>
            </div>
        </div>
    </div>
</body>
</html>
EOF

    # 创建PHP测试页面
    cat > /website/index.php << 'EOF'
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Web Server 1 - PHP Info</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 40px; background: #f5f5f5; }
        .container { max-width: 800px; margin: 0 auto; background: white; padding: 30px; border-radius: 10px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
        .header { background: #e74c3c; color: white; padding: 20px; border-radius: 5px; text-align: center; margin-bottom: 20px; }
        .info { margin: 20px 0; }
        .back-link { display: inline-block; background: #3498db; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px; margin-top: 20px; }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>Welcome to Web Server 1</h1>
            <p>PHP信息页面</p>
        </div>
        <div class="info">
            <h2>服务器信息</h2>
            <p><strong>IP地址:</strong> ***************</p>
            <p><strong>域名:</strong> web.mufeng.yuanchu</p>
            <p><strong>PHP版本:</strong> <?php echo phpversion(); ?></p>
            <p><strong>当前时间:</strong> <?php echo date('Y-m-d H:i:s'); ?></p>
            <p><strong>服务器软件:</strong> <?php echo $_SERVER['SERVER_SOFTWARE']; ?></p>
            <a href="index.html" class="back-link">返回主页</a>
        </div>
    </div>
</body>
</html>
EOF

    # 创建简单的PHP测试页面
    cat > /website/test.php << 'EOF'
<?php
echo "<h1>Welcome to Web Server 1</h1>";
echo "<p>Server IP: ***************</p>";
echo "<p>Domain: web.mufeng.yuanchu</p>";
echo "<p>Service: Apache + MariaDB + WordPress</p>";
echo "<p>PHP Version: " . phpversion() . "</p>";
echo "<p>Current Time: " . date('Y-m-d H:i:s') . "</p>";
?>
EOF

    # 设置权限
    chown -R www-data:www-data /website
    chmod -R 755 /website

    print_info "Apache配置完成"
}

# 安装MariaDB
install_mariadb() {
    print_info "正在安装MariaDB..."

    # 安装MariaDB (Ubuntu方式)
    apt install -y mariadb-server mariadb-client

    # 启动MariaDB
    systemctl enable mariadb
    systemctl start mariadb

    print_info "MariaDB安装完成"
}

# 配置MariaDB
configure_mariadb() {
    print_info "正在配置MariaDB..."

    # 设置MariaDB root密码
    read -s -p "请输入MariaDB root密码: " MYSQL_ROOT_PASSWORD
    echo ""

    # 运行安全配置脚本
    mysql_secure_installation << EOF

y
$MYSQL_ROOT_PASSWORD
$MYSQL_ROOT_PASSWORD
y
y
y
y
EOF

    # 创建用户user1
    mysql -u root -p$MYSQL_ROOT_PASSWORD << EOF
CREATE USER 'user1'@'localhost' IDENTIFIED BY '123456';
GRANT ALL PRIVILEGES ON *.* TO 'user1'@'localhost';
FLUSH PRIVILEGES;
EXIT;
EOF

    print_info "MariaDB配置完成，用户user1已创建，密码为123456"
}

# 安装WordPress
install_wordpress() {
    print_info "正在安装WordPress..."

    # 安装wget
    apt install -y wget

    # 下载WordPress
    cd /tmp
    wget https://wordpress.org/latest.tar.gz

    # 解压WordPress
    tar -xzvf latest.tar.gz -C /website/
    mv /website/wordpress/* /website/
    rm -rf /website/wordpress

    # 创建WordPress数据库
    read -s -p "请输入MariaDB root密码: " MYSQL_ROOT_PASSWORD
    echo ""

    mysql -u root -p$MYSQL_ROOT_PASSWORD << EOF
CREATE DATABASE wordpress;
GRANT ALL PRIVILEGES ON wordpress.* TO 'user1'@'localhost';
FLUSH PRIVILEGES;
EXIT;
EOF

    # 配置WordPress
    cp /website/wp-config-sample.php /website/wp-config.php

    # 修改数据库配置
    sed -i "s/database_name_here/wordpress/" /website/wp-config.php
    sed -i "s/username_here/user1/" /website/wp-config.php
    sed -i "s/password_here/123456/" /website/wp-config.php

    # 生成安全密钥
    KEYS=$(curl -s https://api.wordpress.org/secret-key/1.1/salt/)
    KEYS=$(echo "$KEYS" | sed "s/'/\\\'/g")
    sed -i "/define( 'AUTH_KEY'/,/define( 'NONCE_SALT'/d" /website/wp-config.php
    sed -i "/put your unique phrase here/a\\$KEYS" /website/wp-config.php

    # 设置权限 (Ubuntu方式)
    chown -R www-data:www-data /website
    chmod -R 755 /website

    print_info "WordPress安装完成"
}

# 配置防火墙
configure_firewall() {
    print_info "正在配置防火墙..."

    # 检查ufw状态
    if ! ufw status | grep -q "Status: active"; then
        ufw --force enable
    fi

    # 添加HTTP服务到防火墙
    ufw allow 80/tcp
    ufw allow 'Apache Full'

    print_info "防火墙配置完成"
}

# 启动Apache
start_apache() {
    print_info "正在启动Apache..."

    # 启动服务 (Ubuntu方式)
    systemctl enable apache2
    systemctl restart apache2

    # 检查服务状态
    if systemctl is-active --quiet apache2; then
        print_info "Apache服务启动成功"
    else
        print_error "Apache服务启动失败"
        exit 1
    fi
}

# 测试Web服务
test_web() {
    print_info "正在测试Web服务..."

    # 测试Apache
    curl -s http://localhost/index.php | grep -q "Welcome to Web Server 1"
    if [ $? -eq 0 ]; then
        print_info "Web服务器测试成功"
    else
        print_warning "Web服务器测试失败"
    fi

    print_info "Web服务测试完成"
    print_info "请在浏览器中访问 http://web.mufeng.yuanchu 完成WordPress安装"
}

# 主函数
main() {
    print_info "开始配置Server02 - WEB服务器1 (Ubuntu)..."

    # 获取IP配置
    get_ip_config

    # 询问是否添加服务IP地址
    read -p "是否为WEB服务添加IP地址$SERVER_IP/$SUBNET_MASK? (不会影响现有网络配置) (y/n): " answer
    if [[ "$answer" =~ ^[Yy]$ ]]; then
        setup_ip
    fi

    # 安装和配置服务
    install_apache_php
    configure_apache
    install_mariadb
    configure_mariadb
    install_wordpress
    configure_firewall
    start_apache
    test_web

    # 添加本地hosts条目
    print_info "添加本地hosts条目..."
    if ! grep -q "web.mufeng.yuanchu" /etc/hosts; then
        echo "$SERVER_IP web.mufeng.yuanchu" >> /etc/hosts
        print_info "已添加hosts条目"
    else
        print_info "hosts条目已存在"
    fi

    # 测试Web服务
    print_info "测试Web服务..."
    sleep 3

    # 测试HTTP访问
    if curl -s http://127.0.0.1/ | grep -q "Welcome to Web Server 1"; then
        print_success "HTTP访问测试成功"
    else
        print_warning "HTTP访问测试失败"
    fi

    # 测试PHP
    if curl -s http://127.0.0.1/test.php | grep -q "Welcome to Web Server 1"; then
        print_success "PHP测试成功"
    else
        print_warning "PHP测试失败"
    fi

    print_info "Server02 - WEB服务器1配置完成!"
    print_info "服务器地址: $SERVER_IP"
    print_info "访问地址: http://web.mufeng.yuanchu 或 http://$SERVER_IP"
    print_info "服务: Apache + MariaDB + WordPress"
    print_info "端口: 80"
    print_info "测试页面:"
    print_info "  - 主页: http://$SERVER_IP/"
    print_info "  - PHP信息: http://$SERVER_IP/index.php"
    print_info "  - PHP测试: http://$SERVER_IP/test.php"
    print_info "请确保DNS服务器已正确配置web.mufeng.yuanchu指向$SERVER_IP"
}

# 执行主函数
main
