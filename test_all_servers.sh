#!/bin/bash
# 测试所有服务器配置脚本
# 用于验证DNS、WEB服务器1、WEB服务器2的配置是否正确

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[0;33m'
NC='\033[0m' # No Color

# 全局变量
DNS_IP=""
WEB1_IP=""
WEB2_IP=""

# 打印带颜色的信息
print_info() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

# 设置固定的服务器IP配置
get_server_ips() {
    print_info "固定服务器IP配置："

    # 固定IP分配
    DNS_IP="***************"
    WEB1_IP="***************"
    WEB2_IP="***************"

    print_info "DNS服务器IP: $DNS_IP"
    print_info "WEB服务器1 IP: $WEB1_IP"
    print_info "WEB服务器2 IP: $WEB2_IP"
    print_info "域名: mufeng.yuanchu"
    echo ""
}

# 网络连通性测试
test_network_connectivity() {
    print_info "测试网络连通性..."

    # 测试到各服务器的ping连通性
    if ping -c 3 $DNS_IP >/dev/null 2>&1; then
        print_success "DNS服务器网络连通性正常"
    else
        print_error "DNS服务器网络连通性异常"
    fi

    if ping -c 3 $WEB1_IP >/dev/null 2>&1; then
        print_success "WEB服务器1网络连通性正常"
    else
        print_error "WEB服务器1网络连通性异常"
    fi

    if ping -c 3 $WEB2_IP >/dev/null 2>&1; then
        print_success "WEB服务器2网络连通性正常"
    else
        print_error "WEB服务器2网络连通性异常"
    fi
}

# 测试DNS服务器
test_dns_server() {
    print_info "测试DNS服务器 ($DNS_IP)..."

    # 测试DNS服务是否运行
    if nc -z -w3 $DNS_IP 53 2>/dev/null; then
        print_success "DNS服务端口53可达"
    else
        print_error "DNS服务端口53不可达"
        return 1
    fi

    # 测试DNS解析
    print_info "测试DNS解析..."

    # 测试dns.mufeng.yuanchu
    DNS_RESULT=$(dig @$DNS_IP dns.mufeng.yuanchu +short 2>/dev/null)
    if echo "$DNS_RESULT" | grep -q "$DNS_IP"; then
        print_success "dns.mufeng.yuanchu 解析正常 -> $DNS_RESULT"
    else
        print_error "dns.mufeng.yuanchu 解析失败"
    fi

    # 测试web.mufeng.yuanchu
    WEB1_RESULT=$(dig @$DNS_IP web.mufeng.yuanchu +short 2>/dev/null)
    if [ ! -z "$WEB1_RESULT" ]; then
        print_success "web.mufeng.yuanchu 解析正常 -> $WEB1_RESULT"
        if [ "$WEB1_RESULT" != "$WEB1_IP" ]; then
            print_warning "DNS解析结果与预期IP不符 (预期: $WEB1_IP, 实际: $WEB1_RESULT)"
        fi
    else
        print_error "web.mufeng.yuanchu 解析失败"
    fi

    # 测试www.mufeng.yuanchu
    WEB2_RESULT=$(dig @$DNS_IP www.mufeng.yuanchu +short 2>/dev/null)
    if [ ! -z "$WEB2_RESULT" ]; then
        print_success "www.mufeng.yuanchu 解析正常 -> $WEB2_RESULT"
        if [ "$WEB2_RESULT" != "$WEB2_IP" ]; then
            print_warning "DNS解析结果与预期IP不符 (预期: $WEB2_IP, 实际: $WEB2_RESULT)"
        fi
    else
        print_error "www.mufeng.yuanchu 解析失败"
    fi
}

# 测试WEB服务器1
test_web_server1() {
    print_info "测试WEB服务器1 ($WEB1_IP)..."

    # 测试端口80是否开放
    if nc -z -w3 $WEB1_IP 80 2>/dev/null; then
        print_success "WEB服务器1 端口80可达"
    else
        print_error "WEB服务器1 端口80不可达"
        return 1
    fi

    # 测试HTTP连接
    HTTP_CODE=$(curl -s -o /dev/null -w "%{http_code}" http://$WEB1_IP 2>/dev/null)
    if [ "$HTTP_CODE" = "200" ]; then
        print_success "WEB服务器1 HTTP服务正常 (状态码: $HTTP_CODE)"
    else
        print_error "WEB服务器1 HTTP服务异常 (状态码: $HTTP_CODE)"
    fi

    # 测试PHP页面
    PHP_CONTENT=$(curl -s http://$WEB1_IP/index.php 2>/dev/null)
    if echo "$PHP_CONTENT" | grep -q "Welcome to Web Server 1"; then
        print_success "WEB服务器1 PHP服务正常"
    else
        print_error "WEB服务器1 PHP服务异常"
    fi
}

# 测试WEB服务器2
test_web_server2() {
    print_info "测试WEB服务器2 ($WEB2_IP)..."

    # 测试端口80和443是否开放
    if nc -z -w3 $WEB2_IP 80 2>/dev/null; then
        print_success "WEB服务器2 端口80可达"
    else
        print_error "WEB服务器2 端口80不可达"
    fi

    if nc -z -w3 $WEB2_IP 443 2>/dev/null; then
        print_success "WEB服务器2 端口443可达"
    else
        print_error "WEB服务器2 端口443不可达"
        return 1
    fi

    # 测试HTTP重定向
    HTTP_CODE=$(curl -s -o /dev/null -w "%{http_code}" http://$WEB2_IP 2>/dev/null)
    if [ "$HTTP_CODE" = "301" ] || [ "$HTTP_CODE" = "302" ]; then
        print_success "WEB服务器2 HTTP重定向正常 (状态码: $HTTP_CODE)"
    else
        print_warning "WEB服务器2 HTTP重定向异常 (状态码: $HTTP_CODE)"
    fi

    # 测试HTTPS主页
    HTTPS_CONTENT=$(curl -s -k https://$WEB2_IP/yuanchu.html 2>/dev/null)
    if echo "$HTTPS_CONTENT" | grep -q "Welcome to Mufeng's Website"; then
        print_success "WEB服务器2 HTTPS主页正常"
    else
        print_error "WEB服务器2 HTTPS主页异常"
    fi

    # 测试虚拟目录
    VIRTUAL_CONTENT=$(curl -s -k https://$WEB2_IP/xuni/ 2>/dev/null)
    if echo "$VIRTUAL_CONTENT" | grep -q "Mufeng Information"; then
        print_success "WEB服务器2 虚拟目录正常"
    else
        print_error "WEB服务器2 虚拟目录异常"
    fi
}

# 生成测试报告
generate_report() {
    print_info "生成测试报告..."

    REPORT_FILE="server_test_report_$(date +%Y%m%d_%H%M%S).txt"

    cat > $REPORT_FILE << EOF
服务器配置测试报告
生成时间: $(date)

测试配置:
- DNS服务器IP: $DNS_IP
- WEB服务器1 IP: $WEB1_IP
- WEB服务器2 IP: $WEB2_IP

测试项目:
1. 网络连通性测试
2. DNS服务器功能测试
3. WEB服务器1功能测试
4. WEB服务器2功能测试

详细结果请查看控制台输出。

建议:
- 如有测试失败项目，请检查对应服务器配置
- 确保防火墙规则正确配置
- 验证DNS解析配置是否正确
EOF

    print_success "测试报告已生成: $REPORT_FILE"
}

# 主函数
main() {
    print_info "开始测试所有服务器配置..."
    echo ""

    # 检查必要工具
    MISSING_TOOLS=""

    if ! command -v dig >/dev/null 2>&1; then
        MISSING_TOOLS="$MISSING_TOOLS dig"
    fi

    if ! command -v curl >/dev/null 2>&1; then
        MISSING_TOOLS="$MISSING_TOOLS curl"
    fi

    if ! command -v nc >/dev/null 2>&1; then
        MISSING_TOOLS="$MISSING_TOOLS nc"
    fi

    if [ ! -z "$MISSING_TOOLS" ]; then
        print_error "缺少必要工具: $MISSING_TOOLS"
        print_info "请安装缺少的工具:"
        print_info "Ubuntu: sudo apt install dnsutils curl netcat-openbsd"
        print_info "CentOS: sudo dnf install bind-utils curl nmap-ncat"
        exit 1
    fi

    # 获取服务器IP配置
    get_server_ips

    # 执行各项测试
    test_network_connectivity
    echo ""
    test_dns_server
    echo ""
    test_web_server1
    echo ""
    test_web_server2
    echo ""

    # 生成测试报告
    generate_report

    print_info "所有服务器测试完成!"
    print_info "如有问题，请参考使用说明.md进行故障排除"
}

# 执行主函数
main
