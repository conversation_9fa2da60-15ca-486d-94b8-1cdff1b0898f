#!/bin/bash
# 客户端DNS配置脚本
# 自动配置客户端DNS设置以访问mufeng.yuanchu服务

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[0;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 服务器配置
DNS_SERVER="192.168.182.128"
WEB1_SERVER="192.168.182.130"
WEB2_SERVER="192.168.182.131"
DOMAIN="mufeng.yuanchu"

# 打印带颜色的信息
print_info() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_header() {
    echo -e "${BLUE}[HEADER]${NC} $1"
}

# 检测操作系统
detect_os() {
    if [[ "$OSTYPE" == "linux-gnu"* ]]; then
        if [ -f /etc/debian_version ]; then
            OS="ubuntu"
        elif [ -f /etc/redhat-release ]; then
            OS="centos"
        else
            OS="linux"
        fi
    elif [[ "$OSTYPE" == "darwin"* ]]; then
        OS="macos"
    elif [[ "$OSTYPE" == "msys" ]] || [[ "$OSTYPE" == "cygwin" ]]; then
        OS="windows"
    else
        OS="unknown"
    fi
}

# Ubuntu/Debian DNS配置
configure_ubuntu_dns() {
    print_info "配置Ubuntu/Debian DNS设置..."

    # 备份原始配置
    if [ -f /etc/resolv.conf ]; then
        cp /etc/resolv.conf /etc/resolv.conf.backup
    fi

    # 检查是否使用systemd-resolved
    if systemctl is-active --quiet systemd-resolved; then
        print_info "检测到systemd-resolved，配置resolved.conf..."

        # 配置systemd-resolved
        cat > /etc/systemd/resolved.conf << EOF
[Resolve]
DNS=$DNS_SERVER *******
Domains=$DOMAIN
EOF

        systemctl restart systemd-resolved

    else
        # 直接配置resolv.conf
        print_info "配置resolv.conf..."
        cat > /etc/resolv.conf << EOF
nameserver $DNS_SERVER
nameserver *******
search $DOMAIN
EOF
    fi

    print_success "Ubuntu/Debian DNS配置完成"
}

# CentOS/RHEL DNS配置
configure_centos_dns() {
    print_info "配置CentOS/RHEL DNS设置..."

    # 使用NetworkManager配置DNS
    if command -v nmcli >/dev/null 2>&1; then
        print_info "使用NetworkManager配置DNS..."

        # 获取活动连接
        CONNECTION=$(nmcli -t -f NAME connection show --active | head -1)

        if [ ! -z "$CONNECTION" ]; then
            nmcli connection modify "$CONNECTION" ipv4.dns "$DNS_SERVER,*******"
            nmcli connection modify "$CONNECTION" ipv4.dns-search "$DOMAIN"
            nmcli connection up "$CONNECTION"
            print_success "NetworkManager DNS配置完成"
        else
            print_error "未找到活动的网络连接"
        fi
    else
        # 手动配置resolv.conf
        print_info "手动配置resolv.conf..."
        cp /etc/resolv.conf /etc/resolv.conf.backup
        cat > /etc/resolv.conf << EOF
nameserver $DNS_SERVER
nameserver *******
search $DOMAIN
EOF
        print_success "CentOS/RHEL DNS配置完成"
    fi
}

# 配置hosts文件（备用方案）
configure_hosts() {
    print_info "配置hosts文件作为备用方案..."

    # 备份hosts文件
    cp /etc/hosts /etc/hosts.backup

    # 添加域名解析
    cat >> /etc/hosts << EOF

# Mufeng.yuanchu 服务器配置
$DNS_SERVER dns.$DOMAIN
$WEB1_SERVER web.$DOMAIN
$WEB2_SERVER www.$DOMAIN
EOF

    print_success "hosts文件配置完成"
}

# Windows DNS配置
configure_windows_dns() {
    print_info "配置Windows DNS设置..."
    print_warning "Windows系统需要手动配置DNS"

    echo ""
    print_info "Windows DNS配置步骤："
    echo "1. 打开控制面板 -> 网络和Internet -> 网络和共享中心"
    echo "2. 点击'更改适配器设置'"
    echo "3. 右键点击当前网络连接，选择'属性'"
    echo "4. 选择'Internet协议版本4(TCP/IPv4)'，点击'属性'"
    echo "5. 选择'使用下面的DNS服务器地址'"
    echo "6. 首选DNS服务器: $DNS_SERVER"
    echo "7. 备用DNS服务器: *******"
    echo "8. 点击'确定'保存设置"
    echo ""

    print_info "或者使用PowerShell命令（以管理员身份运行）："
    echo "Get-NetAdapter | Set-DnsClientServerAddress -ServerAddresses $DNS_SERVER,*******"
    echo ""

    print_info "Windows hosts文件配置："
    echo "文件位置: C:\\Windows\\System32\\drivers\\etc\\hosts"
    echo "添加以下内容："
    echo "$DNS_SERVER dns.$DOMAIN"
    echo "$WEB1_SERVER web.$DOMAIN"
    echo "$WEB2_SERVER www.$DOMAIN"
    echo ""
}

# macOS DNS配置
configure_macos_dns() {
    print_info "配置macOS DNS设置..."
    print_warning "macOS系统需要手动配置DNS"

    echo ""
    print_info "macOS DNS配置步骤："
    echo "1. 打开系统偏好设置 -> 网络"
    echo "2. 选择当前网络连接，点击'高级'"
    echo "3. 切换到'DNS'标签页"
    echo "4. 点击'+'添加DNS服务器"
    echo "5. 添加: $DNS_SERVER"
    echo "6. 添加: *******"
    echo "7. 点击'好'保存设置"
    echo ""

    print_info "或者使用命令行配置："
    echo "sudo networksetup -setdnsservers Wi-Fi $DNS_SERVER *******"
    echo ""

    print_info "macOS hosts文件配置："
    echo "sudo vim /etc/hosts"
    echo "添加以下内容："
    echo "$DNS_SERVER dns.$DOMAIN"
    echo "$WEB1_SERVER web.$DOMAIN"
    echo "$WEB2_SERVER www.$DOMAIN"
    echo ""
}

# 检查网络连通性
check_network_connectivity() {
    print_header "检查网络连通性..."

    # 检查到DNS服务器的连通性
    if ping -c 3 $DNS_SERVER >/dev/null 2>&1; then
        print_success "DNS服务器 ($DNS_SERVER) 连通正常"
    else
        print_error "DNS服务器 ($DNS_SERVER) 连通失败"
        print_warning "请检查网络配置或防火墙设置"
        return 1
    fi

    # 检查到WEB服务器的连通性
    if ping -c 3 $WEB1_SERVER >/dev/null 2>&1; then
        print_success "WEB服务器1 ($WEB1_SERVER) 连通正常"
    else
        print_warning "WEB服务器1 ($WEB1_SERVER) 连通失败"
    fi

    if ping -c 3 $WEB2_SERVER >/dev/null 2>&1; then
        print_success "WEB服务器2 ($WEB2_SERVER) 连通正常"
    else
        print_warning "WEB服务器2 ($WEB2_SERVER) 连通失败"
    fi
}

# 测试服务端口
test_service_ports() {
    print_header "测试服务端口..."

    # 测试DNS端口
    if command -v nc >/dev/null 2>&1; then
        if nc -z -w3 $DNS_SERVER 53 2>/dev/null; then
            print_success "DNS服务端口53可达"
        else
            print_error "DNS服务端口53不可达"
        fi

        # 测试HTTP端口
        if nc -z -w3 $WEB1_SERVER 80 2>/dev/null; then
            print_success "WEB服务器1 HTTP端口80可达"
        else
            print_error "WEB服务器1 HTTP端口80不可达"
        fi

        # 测试HTTPS端口
        if nc -z -w3 $WEB2_SERVER 443 2>/dev/null; then
            print_success "WEB服务器2 HTTPS端口443可达"
        else
            print_error "WEB服务器2 HTTPS端口443不可达"
        fi
    else
        print_warning "nc命令不可用，跳过端口测试"
    fi
}

# 测试HTTP服务
test_http_services() {
    print_header "测试HTTP服务..."

    if command -v curl >/dev/null 2>&1; then
        # 测试WEB服务器1
        HTTP_CODE=$(curl -s -o /dev/null -w "%{http_code}" http://$WEB1_SERVER 2>/dev/null)
        if [ "$HTTP_CODE" = "200" ]; then
            print_success "WEB服务器1 HTTP服务正常 (状态码: $HTTP_CODE)"
        else
            print_warning "WEB服务器1 HTTP服务异常 (状态码: $HTTP_CODE)"
        fi

        # 测试WEB服务器2 HTTPS
        HTTPS_CODE=$(curl -s -k -o /dev/null -w "%{http_code}" https://$WEB2_SERVER 2>/dev/null)
        if [ "$HTTPS_CODE" = "200" ]; then
            print_success "WEB服务器2 HTTPS服务正常 (状态码: $HTTPS_CODE)"
        else
            print_warning "WEB服务器2 HTTPS服务异常 (状态码: $HTTPS_CODE)"
        fi
    else
        print_warning "curl命令不可用，跳过HTTP服务测试"
    fi
}

# 测试DNS配置
test_dns() {
    print_info "测试DNS配置..."

    # 测试DNS解析
    local domains=("dns.$DOMAIN" "web.$DOMAIN" "www.$DOMAIN")

    for domain in "${domains[@]}"; do
        if nslookup $domain >/dev/null 2>&1; then
            print_success "$domain 解析成功"
        else
            print_error "$domain 解析失败"
        fi
    done

    # 测试网络连通性
    print_info "测试网络连通性..."
    if ping -c 3 $DNS_SERVER >/dev/null 2>&1; then
        print_success "DNS服务器 $DNS_SERVER 连通正常"
    else
        print_error "DNS服务器 $DNS_SERVER 连通失败"
    fi
}

# 显示访问信息
show_access_info() {
    print_info "服务访问信息："
    echo ""
    echo "🌐 Web服务器1 (Server02):"
    echo "   - HTTP访问: http://web.$DOMAIN"
    echo "   - IP访问: http://$WEB1_SERVER"
    echo ""
    echo "🔒 Web服务器2 (Server03):"
    echo "   - HTTPS访问: https://www.$DOMAIN"
    echo "   - IP访问: https://$WEB2_SERVER"
    echo "   - 虚拟目录: https://www.$DOMAIN/xuni/"
    echo ""
    echo "🔍 DNS服务器 (Server01):"
    echo "   - DNS服务器: $DNS_SERVER"
    echo ""
}

# 生成客户端配置报告
generate_client_report() {
    print_header "生成客户端配置报告..."

    REPORT_FILE="client_config_report_$(date +%Y%m%d_%H%M%S).txt"

    cat > $REPORT_FILE << EOF
客户端DNS配置报告
生成时间: $(date)
操作系统: $OS

服务器配置:
- DNS服务器: $DNS_SERVER
- WEB服务器1: $WEB1_SERVER
- WEB服务器2: $WEB2_SERVER
- 域名: $DOMAIN

配置项目:
1. DNS服务器配置
2. hosts文件配置（可选）
3. 网络连通性测试
4. 服务端口测试
5. HTTP服务测试
6. DNS解析测试

访问地址:
- WEB服务器1: http://web.$DOMAIN
- WEB服务器2: https://www.$DOMAIN
- 虚拟目录: https://www.$DOMAIN/xuni/

详细结果请查看控制台输出。
EOF

    print_success "客户端配置报告已生成: $REPORT_FILE"
}

# 交互式配置菜单
interactive_menu() {
    print_header "客户端配置交互式菜单"

    echo ""
    echo "请选择要执行的操作:"
    echo "1) 自动配置DNS设置"
    echo "2) 仅配置hosts文件"
    echo "3) 测试网络连通性"
    echo "4) 测试服务端口"
    echo "5) 测试HTTP服务"
    echo "6) 测试DNS解析"
    echo "7) 显示访问信息"
    echo "8) 生成配置报告"
    echo "9) 显示手动配置指导"
    echo "0) 退出"
    echo ""

    read -p "请输入选项 (0-9): " choice

    case $choice in
        1)
            configure_dns_by_os
            ;;
        2)
            configure_hosts
            ;;
        3)
            check_network_connectivity
            ;;
        4)
            test_service_ports
            ;;
        5)
            test_http_services
            ;;
        6)
            test_dns
            ;;
        7)
            show_access_info
            ;;
        8)
            generate_client_report
            ;;
        9)
            show_manual_config_guide
            ;;
        0)
            print_info "退出客户端配置工具"
            exit 0
            ;;
        *)
            print_error "无效选项，请重新选择"
            interactive_menu
            ;;
    esac

    echo ""
    read -p "按回车键继续..."
    interactive_menu
}

# 根据操作系统配置DNS
configure_dns_by_os() {
    case $OS in
        "ubuntu")
            configure_ubuntu_dns
            ;;
        "centos")
            configure_centos_dns
            ;;
        "linux")
            print_warning "未知Linux发行版，使用通用配置..."
            configure_ubuntu_dns
            ;;
        "windows")
            configure_windows_dns
            ;;
        "macos")
            configure_macos_dns
            ;;
        *)
            print_error "不支持的操作系统: $OS"
            show_manual_config_guide
            ;;
    esac
}

# 显示手动配置指导
show_manual_config_guide() {
    print_header "手动配置指导"

    echo ""
    print_info "DNS服务器配置:"
    echo "  首选DNS: $DNS_SERVER"
    echo "  备用DNS: *******"
    echo ""

    print_info "hosts文件配置:"
    echo "  添加以下条目到hosts文件:"
    echo "  $DNS_SERVER dns.$DOMAIN"
    echo "  $WEB1_SERVER web.$DOMAIN"
    echo "  $WEB2_SERVER www.$DOMAIN"
    echo ""

    print_info "hosts文件位置:"
    echo "  Linux/macOS: /etc/hosts"
    echo "  Windows: C:\\Windows\\System32\\drivers\\etc\\hosts"
    echo ""
}

# 主函数
main() {
    print_header "客户端DNS配置工具"
    print_info "用于配置客户端访问 $DOMAIN 服务"
    echo ""

    # 检测操作系统
    detect_os
    print_info "检测到操作系统: $OS"
    echo ""

    # 检查网络连通性
    check_network_connectivity
    echo ""

    # 根据操作系统决定是否需要root权限
    if [[ "$OS" == "ubuntu" ]] || [[ "$OS" == "centos" ]] || [[ "$OS" == "linux" ]]; then
        if [ "$EUID" -ne 0 ]; then
            print_warning "Linux系统建议以root用户运行此脚本以自动配置DNS"
            print_info "或者选择手动配置选项"
            echo ""
        fi
    fi

    # 询问配置方式
    echo "请选择配置方式:"
    echo "1) 自动配置 (推荐)"
    echo "2) 交互式菜单"
    echo "3) 仅显示配置指导"
    echo ""

    read -p "请输入选项 (1-3): " config_choice

    case $config_choice in
        1)
            # 自动配置
            if [[ "$OS" == "ubuntu" ]] || [[ "$OS" == "centos" ]] || [[ "$OS" == "linux" ]]; then
                if [ "$EUID" -eq 0 ]; then
                    configure_dns_by_os

                    # 询问是否配置hosts文件
                    read -p "是否配置hosts文件作为备用方案? (y/n): " hosts_choice
                    if [[ "$hosts_choice" =~ ^[Yy]$ ]]; then
                        configure_hosts
                    fi
                else
                    print_warning "需要root权限进行自动配置，显示手动配置指导"
                    show_manual_config_guide
                fi
            else
                configure_dns_by_os
            fi

            # 测试配置
            echo ""
            print_info "等待DNS配置生效..."
            sleep 3
            test_dns
            echo ""
            test_service_ports
            echo ""
            test_http_services
            echo ""

            # 显示访问信息
            show_access_info

            # 生成报告
            generate_client_report

            print_success "客户端DNS配置完成!"
            ;;
        2)
            # 交互式菜单
            interactive_menu
            ;;
        3)
            # 仅显示配置指导
            show_manual_config_guide
            show_access_info
            ;;
        *)
            print_error "无效选项"
            exit 1
            ;;
    esac
}

# 执行主函数
main
