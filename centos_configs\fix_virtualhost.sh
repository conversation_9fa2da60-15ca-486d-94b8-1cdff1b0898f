#!/bin/bash
# 修复CentOS Server03虚拟主机配置

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[0;33m'
BLUE='\033[0;34m'
NC='\033[0m'

print_info() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

print_header() {
    echo -e "${BLUE}[HEADER]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

SERVER_IP="***************"
DOMAIN="mufeng.yuanchu"

print_header "修复虚拟主机配置..."

# 1. 创建正确的HTTPS虚拟主机配置
print_info "创建HTTPS虚拟主机配置..."
cat > /etc/httpd/conf.d/www-ssl.conf << EOF
# HTTPS虚拟主机配置
<VirtualHost *:443>
    ServerName $SERVER_IP
    ServerAlias www.$DOMAIN
    ServerAlias $DOMAIN
    DocumentRoot /var/www/yuanchu
    
    # SSL配置
    SSLEngine on
    SSLCertificateFile /etc/pki/tls/certs/www.$DOMAIN.crt
    SSLCertificateKeyFile /etc/pki/tls/private/www.$DOMAIN.key
    
    # 安全头
    Header always set Strict-Transport-Security "max-age=63072000; includeSubDomains; preload"
    Header always set X-Frame-Options DENY
    Header always set X-Content-Type-Options nosniff
    
    # 日志配置
    ErrorLog /var/log/httpd/www-ssl-error.log
    CustomLog /var/log/httpd/www-ssl-access.log combined
    
    # 目录配置
    <Directory "/var/www/yuanchu">
        AllowOverride None
        Require all granted
        Options Indexes FollowSymLinks
        DirectoryIndex yuanchu.html index.html
    </Directory>
    
    # 虚拟目录配置
    Alias /xuni /var/www/yuanchu/xuni
    <Directory "/var/www/yuanchu/xuni">
        AllowOverride None
        Require all granted
        Options Indexes FollowSymLinks
        DirectoryIndex index.html
    </Directory>
</VirtualHost>
EOF

# 2. 创建HTTP重定向配置
print_info "创建HTTP重定向配置..."
cat > /etc/httpd/conf.d/www-redirect.conf << EOF
# HTTP重定向到HTTPS
<VirtualHost *:80>
    ServerName $SERVER_IP
    ServerAlias www.$DOMAIN
    ServerAlias $DOMAIN
    DocumentRoot /var/www/yuanchu
    
    # 重定向所有HTTP请求到HTTPS
    RewriteEngine On
    RewriteCond %{HTTPS} off
    RewriteRule ^(.*)$ https://%{HTTP_HOST}%{REQUEST_URI} [R=301,L]
    
    # 日志配置
    ErrorLog /var/log/httpd/www-redirect-error.log
    CustomLog /var/log/httpd/www-redirect-access.log combined
</VirtualHost>
EOF

# 3. 修改主配置文件，确保默认虚拟主机不干扰
print_info "修改主配置文件..."
if ! grep -q "ServerName $SERVER_IP" /etc/httpd/conf/httpd.conf; then
    echo "ServerName $SERVER_IP" >> /etc/httpd/conf/httpd.conf
fi

# 4. 禁用默认的welcome页面
print_info "禁用默认welcome页面..."
if [ -f "/etc/httpd/conf.d/welcome.conf" ]; then
    mv /etc/httpd/conf.d/welcome.conf /etc/httpd/conf.d/welcome.conf.bak
    print_info "已备份并禁用welcome.conf"
fi

# 5. 测试配置
print_info "测试Apache配置..."
if httpd -t; then
    print_success "Apache配置测试通过"
else
    echo "配置测试失败，请检查配置文件"
    exit 1
fi

# 6. 重启Apache服务
print_info "重启Apache服务..."
systemctl restart httpd

if systemctl is-active --quiet httpd; then
    print_success "Apache服务重启成功"
else
    echo "Apache服务重启失败"
    exit 1
fi

# 7. 测试访问
print_info "测试访问..."
echo ""
echo "测试HTTPS主页:"
curl -k -I https://$SERVER_IP/yuanchu.html | head -1

echo ""
echo "测试虚拟目录:"
curl -k -I https://$SERVER_IP/xuni/ | head -1

echo ""
echo "测试HTTP重定向:"
curl -I http://$SERVER_IP/ | head -2

print_success "虚拟主机配置修复完成!"
echo ""
echo "现在请在浏览器中访问:"
echo "- https://$SERVER_IP/yuanchu.html"
echo "- https://$SERVER_IP/xuni/"
echo ""
echo "注意: 使用自签名证书，浏览器会显示安全警告，选择继续访问即可"
EOF

chmod +x /root/fix_virtualhost.sh
