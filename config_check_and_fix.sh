#!/bin/bash
# 配置检查和修复脚本
# 用于检查和修复DNS、WEB服务器的配置问题

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[0;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 全局变量
DNS_IP="***************"
WEB1_IP="***************"
WEB2_IP="***************"
DOMAIN="mufeng.yuanchu"

# 打印带颜色的信息
print_info() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_header() {
    echo -e "${BLUE}[HEADER]${NC} $1"
}

# 检查是否以root用户运行
check_root() {
    if [ "$EUID" -ne 0 ]; then
        print_error "请以root用户运行此脚本"
        exit 1
    fi
}

# 检查网络配置
check_network_config() {
    print_header "检查网络配置..."
    
    # 检查IP地址配置
    INTERFACE=$(ip -o -4 route show to default | awk '{print $5}')
    if [ -z "$INTERFACE" ]; then
        print_error "无法确定网络接口"
        return 1
    fi
    
    print_info "主网络接口: $INTERFACE"
    
    # 检查各服务器IP是否配置
    for IP in $DNS_IP $WEB1_IP $WEB2_IP; do
        if ip addr show $INTERFACE | grep -q "$IP"; then
            print_success "IP $IP 已配置"
        else
            print_warning "IP $IP 未配置"
        fi
    done
    
    # 检查网络连通性
    print_info "检查网络连通性..."
    if ping -c 1 ******* >/dev/null 2>&1; then
        print_success "外网连通性正常"
    else
        print_error "外网连通性异常"
    fi
}

# 检查DNS配置
check_dns_config() {
    print_header "检查DNS配置..."
    
    # 检查BIND是否安装
    if command -v named >/dev/null 2>&1; then
        print_success "BIND已安装"
    else
        print_error "BIND未安装"
        return 1
    fi
    
    # 检查DNS服务状态
    if systemctl is-active --quiet named; then
        print_success "DNS服务运行正常"
    else
        print_error "DNS服务未运行"
        print_info "尝试启动DNS服务..."
        systemctl start named
        if systemctl is-active --quiet named; then
            print_success "DNS服务启动成功"
        else
            print_error "DNS服务启动失败"
            return 1
        fi
    fi
    
    # 检查DNS配置文件
    if [ -f "/etc/named.conf" ]; then
        print_success "DNS主配置文件存在"
        if named-checkconf; then
            print_success "DNS配置文件语法正确"
        else
            print_error "DNS配置文件语法错误"
        fi
    else
        print_error "DNS主配置文件不存在"
    fi
    
    # 检查区域文件
    if [ -f "/var/named/$DOMAIN.zone" ]; then
        print_success "DNS区域文件存在"
        if named-checkzone $DOMAIN /var/named/$DOMAIN.zone; then
            print_success "DNS区域文件语法正确"
        else
            print_error "DNS区域文件语法错误"
        fi
    else
        print_error "DNS区域文件不存在"
    fi
    
    # 检查防火墙配置
    if firewall-cmd --list-services | grep -q dns; then
        print_success "防火墙DNS服务已开放"
    else
        print_warning "防火墙DNS服务未开放"
        print_info "开放DNS服务端口..."
        firewall-cmd --permanent --add-service=dns
        firewall-cmd --reload
        print_success "DNS服务端口已开放"
    fi
}

# 检查WEB服务器配置
check_web_config() {
    print_header "检查WEB服务器配置..."
    
    # 检查Apache是否安装
    if command -v apache2 >/dev/null 2>&1 || command -v httpd >/dev/null 2>&1; then
        print_success "Apache已安装"
    else
        print_error "Apache未安装"
        return 1
    fi
    
    # 检查Apache服务状态
    SERVICE_NAME="apache2"
    if ! systemctl list-unit-files | grep -q apache2.service; then
        SERVICE_NAME="httpd"
    fi
    
    if systemctl is-active --quiet $SERVICE_NAME; then
        print_success "Apache服务运行正常"
    else
        print_error "Apache服务未运行"
        print_info "尝试启动Apache服务..."
        systemctl start $SERVICE_NAME
        if systemctl is-active --quiet $SERVICE_NAME; then
            print_success "Apache服务启动成功"
        else
            print_error "Apache服务启动失败"
            return 1
        fi
    fi
    
    # 检查PHP配置
    if command -v php >/dev/null 2>&1; then
        print_success "PHP已安装 (版本: $(php -v | head -n1 | cut -d' ' -f2))"
    else
        print_error "PHP未安装"
    fi
    
    # 检查SSL模块
    if apache2ctl -M 2>/dev/null | grep -q ssl_module; then
        print_success "SSL模块已启用"
    else
        print_warning "SSL模块未启用"
    fi
    
    # 检查防火墙配置
    if command -v ufw >/dev/null 2>&1; then
        # Ubuntu系统
        if ufw status | grep -q "80/tcp"; then
            print_success "防火墙HTTP端口已开放"
        else
            print_warning "防火墙HTTP端口未开放"
        fi
        
        if ufw status | grep -q "443/tcp"; then
            print_success "防火墙HTTPS端口已开放"
        else
            print_warning "防火墙HTTPS端口未开放"
        fi
    else
        # CentOS系统
        if firewall-cmd --list-services | grep -q http; then
            print_success "防火墙HTTP服务已开放"
        else
            print_warning "防火墙HTTP服务未开放"
        fi
    fi
}

# 检查域名解析
check_domain_resolution() {
    print_header "检查域名解析..."
    
    # 检查本地hosts文件
    if grep -q "$DOMAIN" /etc/hosts; then
        print_success "本地hosts文件包含域名配置"
        grep "$DOMAIN" /etc/hosts
    else
        print_warning "本地hosts文件不包含域名配置"
    fi
    
    # 测试DNS解析
    if command -v dig >/dev/null 2>&1; then
        print_info "测试DNS解析..."
        
        # 测试各子域名解析
        for subdomain in "dns" "web" "www"; do
            RESULT=$(dig @$DNS_IP $subdomain.$DOMAIN +short 2>/dev/null)
            if [ ! -z "$RESULT" ]; then
                print_success "$subdomain.$DOMAIN 解析正常 -> $RESULT"
            else
                print_error "$subdomain.$DOMAIN 解析失败"
            fi
        done
    else
        print_warning "dig命令不可用，无法测试DNS解析"
    fi
}

# 修复常见问题
fix_common_issues() {
    print_header "修复常见问题..."
    
    # 修复SELinux问题（如果存在）
    if command -v getenforce >/dev/null 2>&1; then
        if [ "$(getenforce)" = "Enforcing" ]; then
            print_warning "SELinux处于强制模式，可能影响服务运行"
            print_info "建议临时设置为宽松模式: setenforce 0"
        fi
    fi
    
    # 修复权限问题
    if [ -d "/var/named" ]; then
        chown -R named:named /var/named 2>/dev/null || true
        chmod 755 /var/named 2>/dev/null || true
    fi
    
    if [ -d "/website" ]; then
        chown -R www-data:www-data /website 2>/dev/null || chown -R apache:apache /website 2>/dev/null || true
        chmod -R 755 /website 2>/dev/null || true
    fi
    
    if [ -d "/www" ]; then
        chown -R www-data:www-data /www 2>/dev/null || chown -R apache:apache /www 2>/dev/null || true
        chmod -R 755 /www 2>/dev/null || true
    fi
    
    print_success "权限修复完成"
}

# 生成配置报告
generate_report() {
    print_header "生成配置报告..."
    
    REPORT_FILE="config_check_report_$(date +%Y%m%d_%H%M%S).txt"
    
    cat > $REPORT_FILE << EOF
服务器配置检查报告
生成时间: $(date)

配置信息:
- DNS服务器IP: $DNS_IP
- WEB服务器1 IP: $WEB1_IP  
- WEB服务器2 IP: $WEB2_IP
- 域名: $DOMAIN

检查项目:
1. 网络配置检查
2. DNS服务配置检查
3. WEB服务器配置检查
4. 域名解析检查
5. 常见问题修复

详细结果请查看控制台输出。

建议:
- 确保所有服务正常运行
- 检查防火墙规则配置
- 验证DNS解析配置
- 测试网站访问功能
EOF
    
    print_success "配置报告已生成: $REPORT_FILE"
}

# 主函数
main() {
    print_info "开始配置检查和修复..."
    echo ""
    
    # 检查root权限
    check_root
    
    # 执行各项检查
    check_network_config
    echo ""
    check_dns_config  
    echo ""
    check_web_config
    echo ""
    check_domain_resolution
    echo ""
    fix_common_issues
    echo ""
    
    # 生成报告
    generate_report
    
    print_info "配置检查和修复完成!"
    print_info "如有问题，请参考生成的报告文件"
}

# 执行主函数
main
