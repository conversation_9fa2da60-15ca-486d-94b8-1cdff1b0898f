#!/bin/bash
# CentOS Server01 - DNS服务器配置脚本
# 系统: CentOS 9
# 功能: BIND DNS服务器，提供域名解析服务
# 包含错误检测和自动修复功能

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[0;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 固定IP配置
SERVER_IP="***************"
SUBNET_MASK="24"
WEB1_IP="***************"
WEB2_IP="***************"
DOMAIN="mufeng.yuanchu"

# 打印带颜色的信息
print_info() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_header() {
    echo -e "${BLUE}[HEADER]${NC} $1"
}

# 检查是否以root用户运行
check_root() {
    if [ "$EUID" -ne 0 ]; then
        print_error "请以root用户运行此脚本"
        exit 1
    fi
}

# 显示固定的服务IP配置
show_config() {
    print_header "CentOS DNS服务器固定IP配置："
    print_info "DNS服务器IP: $SERVER_IP/$SUBNET_MASK"
    print_info "WEB服务器1 IP: $WEB1_IP"
    print_info "WEB服务器2 IP: $WEB2_IP"
    print_info "域名: $DOMAIN"
    echo ""

    read -p "确认使用以上固定配置? (y/n): " confirm
    if [[ ! "$confirm" =~ ^[Yy]$ ]]; then
        print_error "用户取消配置"
        exit 1
    fi
}

# 添加服务IP地址
setup_ip() {
    print_header "配置网络IP地址..."

    # 获取网络接口名称
    INTERFACE=$(ip -o -4 route show to default | awk '{print $5}')

    if [ -z "$INTERFACE" ]; then
        print_error "无法确定网络接口"
        exit 1
    fi

    print_info "在网络接口 $INTERFACE 上添加服务IP地址..."

    # 检查IP是否已经存在
    if ip addr show $INTERFACE | grep -q "$SERVER_IP"; then
        print_warning "IP地址 $SERVER_IP 已经存在于接口 $INTERFACE 上"
        return 0
    fi

    # 添加IP地址到现有接口（不删除原有配置）
    if ip addr add $SERVER_IP/$SUBNET_MASK dev $INTERFACE 2>/dev/null; then
        print_success "成功添加IP地址 $SERVER_IP 到接口 $INTERFACE"
    else
        # 如果添加失败，可能是因为IP已存在，检查是否真的存在
        if ip addr show $INTERFACE | grep -q "$SERVER_IP"; then
            print_warning "IP地址 $SERVER_IP 已经存在，跳过添加"
        else
            print_error "添加IP地址失败"
            exit 1
        fi
    fi

    # 创建持久化配置（CentOS 9方式）
    print_info "创建持久化IP配置..."

    # 创建网络脚本来在启动时添加IP
    cat > /etc/systemd/system/dns-service-ip.service << EOF
[Unit]
Description=Add DNS Service IP Address
After=network.target

[Service]
Type=oneshot
ExecStart=/bin/ip addr add $SERVER_IP/$SUBNET_MASK dev $INTERFACE
RemainAfterExit=yes
StandardOutput=journal

[Install]
WantedBy=multi-user.target
EOF

    # 启用服务
    systemctl enable dns-service-ip.service

    # 验证IP配置
    sleep 2
    print_info "当前接口 $INTERFACE 的IP配置："
    ip addr show $INTERFACE | grep inet

    print_success "服务IP地址添加完成，已配置开机自启"
}

# 配置CentOS软件源
configure_centos_repos() {
    print_header "配置CentOS软件源..."

    # 备份原始repo文件
    if [ ! -d "/etc/yum.repos.d.backup" ]; then
        cp -r /etc/yum.repos.d /etc/yum.repos.d.backup 2>/dev/null || true
        print_info "已备份原始软件源配置"
    fi

    # 检查是否为CentOS Stream
    if grep -q "CentOS Stream" /etc/os-release; then
        print_info "检测到CentOS Stream，配置官方软件源..."

        # 创建CentOS Stream软件源配置
        cat > /etc/yum.repos.d/centos-stream.repo << 'EOF'
[baseos]
name=CentOS Stream 9 - BaseOS
baseurl=http://mirror.stream.centos.org/9-stream/BaseOS/x86_64/os/
gpgcheck=1
enabled=1
gpgkey=file:///etc/pki/rpm-gpg/RPM-GPG-KEY-centosofficial

[appstream]
name=CentOS Stream 9 - AppStream
baseurl=http://mirror.stream.centos.org/9-stream/AppStream/x86_64/os/
gpgcheck=1
enabled=1
gpgkey=file:///etc/pki/rpm-gpg/RPM-GPG-KEY-centosofficial

[extras-common]
name=CentOS Stream 9 - Extras packages
baseurl=http://mirror.stream.centos.org/9-stream/extras-common/x86_64/os/
gpgcheck=1
enabled=1
gpgkey=file:///etc/pki/rpm-gpg/RPM-GPG-KEY-centosofficial
EOF
    fi

    # 配置EPEL仓库
    print_info "配置EPEL仓库..."
    if ! dnf list installed epel-release >/dev/null 2>&1; then
        dnf install -y epel-release || print_warning "EPEL仓库安装失败"
    fi

    # 清理缓存
    print_info "清理软件包缓存..."
    dnf clean all

    # 重建缓存
    print_info "重建软件包缓存..."
    if ! dnf makecache; then
        print_warning "软件源缓存重建失败，尝试使用阿里云镜像..."

        # 备用方案：使用阿里云镜像
        cat > /etc/yum.repos.d/centos-stream.repo << 'EOF'
[baseos]
name=CentOS Stream 9 - BaseOS - Aliyun
baseurl=https://mirrors.aliyun.com/centos-stream/9-stream/BaseOS/x86_64/os/
gpgcheck=1
enabled=1
gpgkey=file:///etc/pki/rpm-gpg/RPM-GPG-KEY-centosofficial

[appstream]
name=CentOS Stream 9 - AppStream - Aliyun
baseurl=https://mirrors.aliyun.com/centos-stream/9-stream/AppStream/x86_64/os/
gpgcheck=1
enabled=1
gpgkey=file:///etc/pki/rpm-gpg/RPM-GPG-KEY-centosofficial
EOF

        dnf clean all
        dnf makecache || print_error "所有软件源配置都失败"
    fi

    print_success "CentOS软件源配置完成"
}

# 安装BIND DNS服务
install_bind() {
    print_header "安装BIND DNS服务..."

    # 检查是否已安装
    if command -v named >/dev/null 2>&1; then
        print_success "BIND已经安装"
        return 0
    fi

    # 检查网络连接
    if ! ping -c 1 ******* >/dev/null 2>&1; then
        print_warning "网络连接异常，请检查网络设置"
    fi

    # 安装BIND DNS服务
    print_info "正在安装BIND DNS服务包..."
    if dnf install -y bind bind-utils; then
        print_success "BIND DNS服务安装成功"
    else
        print_error "BIND DNS服务安装失败"
        return 1
    fi

    # 验证安装结果
    if command -v named >/dev/null 2>&1; then
        print_success "BIND DNS服务安装验证成功"
        return 0
    else
        print_error "BIND DNS服务安装验证失败"
        return 1
    fi
}

# 配置BIND
configure_bind() {
    print_header "配置BIND DNS服务..."

    # 备份原始配置
    cp /etc/named.conf /etc/named.conf.bak 2>/dev/null || true

    # 计算网络地址
    NETWORK=$(echo $SERVER_IP | cut -d. -f1-3).0/$SUBNET_MASK

    # 修改主配置文件
    cat > /etc/named.conf << EOF
//
// named.conf for CentOS
//
// Provided by Red Hat bind package to configure the ISC BIND named(8) DNS
// server as a caching only nameserver (as a localhost DNS resolver only).
//

options {
    listen-on port 53 { 127.0.0.1; $SERVER_IP; any; };
    listen-on-v6 port 53 { ::1; };
    directory "/var/named";
    dump-file "/var/named/data/cache_dump.db";
    statistics-file "/var/named/data/named_stats.txt";
    memstatistics-file "/var/named/data/named_mem_stats.txt";
    secroots-file "/var/named/data/named.secroots";
    recursing-file "/var/named/data/named.recursing";
    allow-query { localhost; any; };

    recursion yes;
    dnssec-validation yes;

    managed-keys-directory "/var/named/dynamic";

    pid-file "/run/named/named.pid";
    session-keyfile "/run/named/session.key";

    /* https://fedoraproject.org/wiki/Changes/CryptoPolicy */
    include "/etc/crypto-policies/back-ends/bind.config";

    forwarders {
        *******;
        *******;
    };
};

logging {
    channel default_debug {
        file "data/named.run";
        severity dynamic;
    };
};

zone "." IN {
    type hint;
    file "named.ca";
};

zone "$DOMAIN" IN {
    type master;
    file "$DOMAIN.zone";
    allow-update { none; };
};

include "/etc/named.rfc1912.zones";
include "/etc/named.root.key";
EOF

    # 创建区域文件
    cat > /var/named/$DOMAIN.zone << EOF
\$TTL 86400
@   IN  SOA     dns.$DOMAIN. admin.$DOMAIN. (
        2024062001  ; Serial
        3600        ; Refresh
        1800        ; Retry
        604800      ; Expire
        86400 )     ; Minimum TTL
;
@       IN  NS      dns.$DOMAIN.
dns     IN  A       $SERVER_IP
web     IN  A       $WEB1_IP
www     IN  A       $WEB2_IP
EOF

    # 设置权限
    chown named:named /var/named/$DOMAIN.zone
    chmod 644 /var/named/$DOMAIN.zone

    print_success "BIND DNS服务配置完成"
}

# 配置防火墙
configure_firewall() {
    print_header "配置CentOS防火墙..."

    # 检查firewalld状态
    if ! systemctl is-active --quiet firewalld; then
        systemctl enable firewalld
        systemctl start firewalld
        print_info "已启动firewalld服务"
    fi

    # 添加DNS服务到防火墙
    firewall-cmd --permanent --add-service=dns
    firewall-cmd --permanent --add-port=53/tcp
    firewall-cmd --permanent --add-port=53/udp

    # 重新加载防火墙配置
    firewall-cmd --reload

    print_success "防火墙配置完成"
}

# 配置SELinux
configure_selinux() {
    print_header "配置SELinux..."

    # 检查SELinux状态
    if command -v getenforce >/dev/null 2>&1; then
        SELINUX_STATUS=$(getenforce)
        print_info "当前SELinux状态: $SELINUX_STATUS"

        if [ "$SELINUX_STATUS" = "Enforcing" ]; then
            print_info "配置SELinux策略以支持BIND..."

            # 设置BIND相关的SELinux上下文
            setsebool -P named_write_master_zones on
            restorecon -R /var/named/

            print_success "SELinux配置完成"
        fi
    else
        print_info "SELinux未安装或不可用"
    fi
}

# 启动DNS服务
start_dns() {
    print_header "启动DNS服务..."

    # 检查配置
    print_info "检查BIND配置文件语法..."
    if named-checkconf; then
        print_success "BIND主配置文件语法正确"
    else
        print_error "BIND主配置文件语法错误"
        exit 1
    fi

    print_info "检查DNS区域文件语法..."
    if named-checkzone $DOMAIN /var/named/$DOMAIN.zone; then
        print_success "DNS区域文件语法正确"
    else
        print_error "DNS区域文件语法错误"
        exit 1
    fi

    # 启动服务
    systemctl enable named
    systemctl start named

    # 检查服务状态
    if systemctl is-active --quiet named; then
        print_success "DNS服务启动成功"
    else
        print_error "DNS服务启动失败"
        journalctl -u named --no-pager -l
        exit 1
    fi
}

# 测试DNS配置
test_dns() {
    print_header "测试DNS配置..."

    # 等待DNS服务完全启动
    sleep 5

    # 测试DNS解析
    print_info "测试DNS解析..."

    # 测试本地解析
    if dig @127.0.0.1 dns.$DOMAIN +short | grep -q "$SERVER_IP"; then
        print_success "dns.$DOMAIN 解析正常"
    else
        print_warning "dns.$DOMAIN 解析异常"
    fi

    if dig @127.0.0.1 web.$DOMAIN +short | grep -q "$WEB1_IP"; then
        print_success "web.$DOMAIN 解析正常"
    else
        print_warning "web.$DOMAIN 解析异常"
    fi

    if dig @127.0.0.1 www.$DOMAIN +short | grep -q "$WEB2_IP"; then
        print_success "www.$DOMAIN 解析正常"
    else
        print_warning "www.$DOMAIN 解析异常"
    fi

    print_success "DNS测试完成"
    print_info "请确保客户端DNS设置为$SERVER_IP"

    # 添加本地hosts条目以便测试
    print_info "添加本地hosts条目..."
    if ! grep -q "$DOMAIN" /etc/hosts; then
        cat >> /etc/hosts << EOF

# $DOMAIN 域名解析
$SERVER_IP dns.$DOMAIN
$WEB1_IP web.$DOMAIN
$WEB2_IP www.$DOMAIN
EOF
        print_success "已添加hosts条目"
    else
        print_info "hosts条目已存在"
    fi
}

# 主函数
main() {
    print_header "开始配置CentOS Server01 - DNS服务器..."
    echo ""

    # 检查root权限
    check_root

    # 显示固定IP配置
    show_config

    # 询问是否添加服务IP地址
    read -p "是否为DNS服务添加IP地址$SERVER_IP/$SUBNET_MASK? (不会影响现有网络配置) (y/n): " answer
    if [[ "$answer" =~ ^[Yy]$ ]]; then
        setup_ip
        echo ""
    fi

    # 配置软件源和安装DNS服务
    configure_centos_repos
    echo ""

    if ! install_bind; then
        print_error "BIND安装失败，无法继续"
        exit 1
    fi
    echo ""

    configure_bind
    echo ""
    configure_firewall
    echo ""
    configure_selinux
    echo ""
    start_dns
    echo ""
    test_dns

    echo ""
    print_success "CentOS Server01 - DNS服务器配置完成!"
    print_info "DNS服务器地址: $SERVER_IP"
    print_info "支持的域名解析:"
    print_info "  - dns.$DOMAIN -> $SERVER_IP"
    print_info "  - web.$DOMAIN -> $WEB1_IP"
    print_info "  - www.$DOMAIN -> $WEB2_IP"
}

# 执行主函数
main "$@"
